Chapter 1 
The Problem and Its Background 
 
The trends in healthcare inventory management have undergone significant transformation in recent years, revolutionizing how medical facilities operate and serve their communities. In the province of Bulacan, the Bulacan Medical Center stands as a pivotal hub for medication services, continuously striving to integrate contemporary approaches to inventory management. The healthcare sector is witnessing a paradigm shift toward data-driven decision-making, with institutions increasingly leveraging advanced analytics to inform inventory strategies. According to <PERSON> et al. (2024), modern inventory optimization techniques like Holt-Winters exponential smoothing and ARIMA models are becoming essential tools for supply chain management, enabling more accurate demand forecasting and reducing stockouts. This evolution includes the adoption of digital solutions such as electronic databases, barcode scanning, and RFID technology, replacing traditional manual processes that have long hindered operational efficiency. 
The integration of Internet of Things (IoT) devices for real-time tracking of medicine inventory represents another significant trend, enabling healthcare facilities to maintain optimal stock levels while minimizing waste. As noted by <PERSON><PERSON><PERSON> et al. (2024), IoT implementation in pharmaceutical inventory tracking has transformed how healthcare facilities monitor and manage their medical supplies, providing real-time visibility and enhancing decision-making capabilities. <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2025) further demonstrated that predictive analytics using machine learning models can effectively forecast healthcare demand by analyzing various factors including seasonality, demographic trends, and market dynamics. <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2024) have also shown that ARIMA models can effectively forecast key performance indicators in hospital settings, including average duration of hospitalization and bed utilization rates, which directly impact medication demand patterns. 
The emergence of ensemble learning methods in forecasting has also gained significant traction in healthcare inventory management. <PERSON><PERSON><PERSON> et al. (2023) highlighted that ensemble deep learning models for time-series demand forecasting can significantly improve inventory optimization by addressing market volatility and customer demand uncertainties. This multi-model approach provides more robust predictions by leveraging the strengths of various forecasting techniques while minimizing their individual weaknesses. Sibindi et al. (2023) further demonstrated that hybrid boosting ensemble models outperform individual algorithms in prediction tasks, suggesting similar benefits could be achieved in medicine demand forecasting. 
In contrast, local healthcare institutions in the Philippines often rely on manual or semidigital inventory processes, with limited use of predictive analytics, real-time monitoring, or AI-enhanced decision-making tools. Additionally, integration of emerging technologies such IoT-based inventory tracking, and ensemble forecasting models remains underexplored in these settings. This technological gap underscores the urgent need for context-adapted solutions like BMC MedForecast, which are designed to function effectively within the constraints of local healthcare systems while leveraging modern predictive and optimization techniques. 
 
The Bulacan Medical Center faces several significant challenges due to its reliance on outdated transaction processes for inventory management, impacting the organization's ability to provide optimal care. One key issue is inaccurate prediction of medicine demand, leading to either shortages that compromise patient care or overstocking those results in expired medications and financial waste. Manual methods severely limit visibility and control over inventory levels, increasing the risk of stockouts or expired medications slipping through the cracks. Furthermore, these manual processes contribute to operational inefficiencies, consuming valuable time and resources that could be better utilized for patient care activities. 
The center also struggles with the "bullwhip effect" in its supply chain, where small variations in demand at the patient level can result in large fluctuations in inventory at the supplier level. This phenomenon, exacerbated by manual ordering processes, leads to inefficient resource allocation and increased costs. Subramanian (2021) identified this as one of the weakest links in health supply chains, noting that "without the ability to forecast demand with certainty, the stakeholders cannot plan and make commitments for the future." Additionally, the lack of integration between inventory management systems and clinical decision support systems creates a disconnect between medication prescribing patterns and stock replenishment strategies, further complicating efficient inventory management. 
As transaction volumes increase, manual processes become increasingly cumbersome and impractical, limiting scalability and adaptability to changing healthcare needs. Bhat et al. (2024) emphasized that inefficient inventory management in public healthcare systems significantly impacts patient care and healthcare costs, particularly in resourceconstrained environments like primary healthcare centers. Muhamediyeva et al. (2024) further highlighted that in a dynamically changing medical landscape with increasing consumer variability, accurately forecasting demand for medical products is critical for pharmaceutical companies and healthcare systems. The challenge is further compounded by the unique nature of pharmaceutical products, each characterized by distinct demand patterns, shelf lives, and levels of criticality, which complicates the inventory management process (Deekshitha et al., 2024). 
The objective of this study is to integrate advanced forecasting techniques for medicine inventory management that enhance the efficiency of requesting medicines based on seasonal variations and quarterly demand patterns. This approach aims to leverage analytics techniques using historical data to generate comprehensive reports on inventory, monitor supply levels, and ensure timely restocking. Continuous monitoring and improvement are key components, involving the establishment of systems to track performance indicators such as inventory turnover, stockout rates, and medication wastage. 
Additionally, the study seeks to enhance inventory optimizations through forecasting algorithm analysis of medicine demand and medicine trends, enabling the identification of reliable suppliers and negotiation of favorable terms. As highlighted by Amosu et al. (2024), AI-driven demand forecasting can significantly improve inventory optimization, cost reduction, and customer satisfaction by analyzing historical sales data, market trends, and external factors such as seasonality and promotions. Juanatas and Juanatas (2024) demonstrated that sales forecasting systems utilizing simple moving averages and inventory requirement values can effectively optimize inventory management in business settings, suggesting similar approaches could benefit healthcare facilities. 
The research aims to develop a comprehensive framework for integrating forecasting models with existing healthcare information systems, addressing implementation challenges in resource-constrained environments like the Bulacan Medical Center. By implementing and evaluating these advanced forecasting techniques in a real-world healthcare setting, this study provided empirical evidence of their effectiveness and practical guidelines for their adoption in similar institutions. Costa et al. (2024) highlighted the importance of applying demand forecasting models to assess inventory management accuracy in specialty pharmacies, noting that optimizing forecasting systems can financially benefit pharmacies by reducing inventory holding costs and minimizing medication expiration. Furthermore, the study aims to establish a systematic approach to inventory categorization based on value, criticality, and availability, striking an optimal balance between stock availability and cost efficiency (Deekshitha et al., 2024). 
This research contributes significantly to the field of pharmacy inventory management through several innovative approaches tailored to the unique challenges faced by rural medical institutions like the Bulacan Medical Center. The development of a multi-algorithm forecasting system that evaluates seven distinct methods (Holt-Winters, Moving Average, Linear Regression, Polynomial Regression, ARIMA, Ensemble Methods, and Gradient Boosting) represents a major advancement in medicine demand prediction. By automatically selecting the most accurate forecasting method for each medicine based on historical data patterns, the system provides unprecedented precision in inventory management. 
Automation of the reordering process is another key contribution, with AI-driven triggers set up to reorder medications when inventory falls below specified thresholds or predicted demand exceeds stock levels. Moreover, AI algorithms are employed to optimize stock rotation, prioritizing medications with shorter shelf lives to minimize wastage and maximize turnover.  
The study also addresses the specific challenges of inventory replenishment patterns in healthcare settings, where occasional large purchases are followed by many small sales. By developing specialized forecasting approaches for these unique patterns, the research offers solutions that are directly applicable to real-world healthcare inventory management. Verma (2024) emphasized how AI is transforming supply chain management through demand forecasting, real-time inventory management, and dynamic optimization, leading to significant improvements in logistics costs, inventory levels, and service delivery. 
Furthermore, the creation of user-friendly interfaces that present complex forecasting results in an accessible manner contributes to the practical adoption of these advanced techniques by healthcare professionals without specialized data science training. Kraljevic et al. (2024) demonstrated that large language models fine-tuned on hospital data can effectively predict medical codes and support various biomedical use cases, suggesting potential applications in inventory management and demand forecasting. Through these methodologies, the study revolutionizes pharmacy inventory management at Bulacan Medical Center, ensuring efficient and effective medication supply chain operations that ultimately improve patient care and resource utilization. As Hao (2024) noted, organizations that successfully integrate demand forecasting and inventory optimization strategies can not only achieve cost savings but also enhance their ability to respond to market fluctuations and disruptions swiftly, creating more resilient and adaptable supply chains in an ever-evolving business environment. 
Review of Related Literature 
Digital Transformation in Healthcare Inventory Management 
Recent studies demonstrate that effective inventory management directly impacts healthcare quality and operational efficiency. Arnaiz et al. (2023) investigated the optimization of inventory management and demand forecasting systems using time series algorithms, finding that properly implemented forecasting models can significantly reduce stockouts while minimizing excess inventory. Their research highlighted that "demand forecasting is crucial in the retail industry, influencing supply chain management, inventory control, and pricing strategies. Accurately predicting demand is essential for optimizing resource allocation, reducing stockouts, and minimizing holding costs" (Arnaiz et al., 2023). 
The application of advanced forecasting techniques in healthcare settings has gained substantial attention. Saha and Rathore (2024) developed a smart inventory management system for hospital supply chains using multi-agent reinforcement learning. Their approach addressed the complex challenge of medication demand dependencies in hospital settings, demonstrating that "a stochastic semi-Markov decision process model solved by multiagent reinforcement learning" can effectively optimize order quantities and inventory control policies. This research represents a significant advancement in modeling the interdependencies between medications prescribed to hospitalized patients—a complexity often overlooked in traditional forecasting approaches. 
The pharmaceutical industry presents unique inventory management challenges due to product perishability, strict regulatory requirements, and the critical nature of medication availability. George and Elrashid (2023) examined inventory management and pharmaceutical supply chain performance in hospital pharmacies in Bahrain, finding a significant positive relationship between inventory level control and PSC performance. Their structural equation modeling approach demonstrated that demand forecasting significantly impacts pharmaceutical supply chain performance, highlighting the strategic importance of forecasting in ensuring medication availability while controlling costs. 
Amosu et al. (2024) explored AI-driven demand forecasting for enhancing inventory management and customer satisfaction. Their research demonstrated how artificial intelligence techniques can improve forecast accuracy and customer service levels by better predicting demand patterns. This integration of AI into forecasting systems represents a significant advancement in healthcare inventory management capabilities. 
Bhat et al. (2024) developed a machine learning stochastic model for inventory management and demand forecasting in primary health services. Their research focused on "optimizing medication access in public healthcare centers" through advanced predictive modeling techniques. This work highlights the growing importance of machine learning approaches in addressing the unique challenges of public healthcare inventory management. 
Methodological Advancements in Healthcare Forecasting 
The methodological landscape for healthcare forecasting has expanded considerably, with researchers exploring various approaches to address the unique challenges of pharmaceutical demand prediction. Time series methods remain foundational in this domain, with significant advancements in their application to healthcare settings. 
Holt-Winter exponential smoothing has emerged as an effective approach for 
pharmaceutical forecasting, particularly for medications with seasonal patterns. Octiva et al. (2024) applied this method to design a drug inventory prediction application for private health units, finding that "the MAPE value was less than 10% for these four drugs, which means that the predictive value has quite high accuracy." Their research demonstrated that the Holt-Winter Exponential Smoothing algorithm can be effectively implemented to predict drug supplies, providing healthcare facilities with reliable forecasts for inventory planning. 
Kumar et al. (2024) conducted a comparative assessment of Holt-Winter exponential smoothing and autoregressive integrated moving average (ARIMA) for inventory optimization in supply chains. Their research found that while ARIMA models generally outperform Holt-Winters methods, their advantage diminishes for medications with strong seasonal patterns or limited historical data. This nuanced finding highlights the importance of method selection based on specific medication characteristics rather than applying a onesize-fits-all approach to pharmaceutical forecasting. 
Machine learning approaches have demonstrated particular promise in healthcare forecasting. Eldred et al. (2023) explored leveraging AI for inventory management and accurate forecasting in an industrial field study. Their research utilized "multiple multilayered machine learning models" to analyze product movement data, product specification data, and activity specification data. The models achieved "78% average accuracy for the top 10 products by volume" and subsequent iterations improved accuracy to 86%, demonstrating the potential of machine learning approaches to significantly enhance forecasting precision. 
Gradient boosting techniques have shown exceptional performance in healthcare forecasting applications. Abdali et al. (2025) developed a priority queueing-inventory approach for inventory management using machine learning algorithms. Their research compared "several machine learning algorithms to prioritize customers" and developed "a mathematical model to determine the allocation policy of on-hand products to each group of customers." This innovative approach demonstrates how advanced machine learning techniques can be applied not only to forecast demand but also to optimize inventory allocation based on customer prioritization. 
Ensemble methods that combine multiple forecasting approaches have demonstrated superior performance compared to individual methods. Seyedan et al. (2023) developed an order-up-to-level inventory optimization model using time-series demand forecasting with ensemble deep learning. Their research found that ensemble approaches reduce forecast error compared to individual methods, challenging the conventional reliance on singlemethod forecasting and suggesting that methodological diversity offers inherent advantages in capturing complex healthcare demand patterns. 
Ganie et al. (2023) developed "an ensemble learning approach for diabetes prediction using boosting techniques." While focused on clinical prediction rather than inventory management, their methodological innovations in ensemble learning have significant implications for healthcare forecasting. Their research demonstrated that "boosting techniques can significantly improve prediction accuracy by combining multiple weak learners into a strong predictive model" (Ganie et al., 2023). 
Mousa and Al-Khateeb (2023) conducted a comprehensive review of deep learning techniques for predicting medicine demand. Their analysis of various deep learning approaches revealed that "deep learning models can capture complex patterns in pharmaceutical demand data that traditional statistical methods might miss" (Mousa & AlKhateeb, 2023). This review provides valuable insights into the relative strengths and limitations of different deep learning architectures for healthcare forecasting applications. 
Mirescu and Popescu (2024) applied the ARIMA method for forecasting performance indicators in health systems. Their research demonstrated the effectiveness of ARIMA models in predicting various healthcare metrics, providing valuable insights for resource planning and performance management. This work highlights the continued relevance of traditional time series methods alongside newer machine learning approaches. 
Costa et al. (2024) examined the effect of applying a demand forecasting model to assess the accuracy of inventory management in a specialty pharmacy. Their research demonstrated that "accurate demand forecasting can significantly improve inventory management performance in specialized pharmaceutical settings" (Costa et al., 2024). This work highlights the particular challenges and opportunities in forecasting for specialty medications. 
Healthcare-Specific Forecasting Challenges and Solutions 
The inventory management presents unique challenges that require specialized forecasting approaches. The irregular demand patterns, critical nature of stockouts, and complex dependencies between medications create a forecasting environment unlike other 
industries. 
Medication demand dependencies represent a significant challenge in healthcare forecasting. Saha and Rathore (2024) addressed this issue by developing a smart inventory management system with medication demand dependencies in a hospital supply chain. Their research recognized that medications are often prescribed in combinations, creating complex interdependencies that must be accounted for in forecasting models. By incorporating these dependencies into their multi-agent reinforcement learning approach, they achieved more accurate forecasts and optimized inventory policies. 
The perishable nature of pharmaceutical products adds another layer of complexity to healthcare forecasting.  
Ahmadi et al. (2022) developed intelligent inventory management approaches for perishable pharmaceutical products in a healthcare supply chain. Their research compared reinforcement learning methods with traditional periodic review policies, finding that "the IIM policies are more cost-effective than the R,s,S policies" and provide "a higher service level for the patients, and a lower risk of product expiration." This research highlights the importance of considering product perishability in healthcare forecasting models. 
Stochastic demand patterns in healthcare settings require specialized modeling approaches. Ternero et al. (2023) proposed an inventory management system based on a periodic review model for a medical equipment company. Their methodology combined "ABC/XYZ classification using the patterns and magnitude of demand under the constraints of spare parts cost." This approach allowed for the reduction of arrears, increased service levels, and decreased average inventory, demonstrating the effectiveness of classification-based approaches for managing stochastic demand in healthcare settings. 
The COVID-19 pandemic highlighted the challenges of healthcare forecasting during external shocks. Rahiminia et al. (2025) developed a novel data-driven patient and medical waste queueing-inventory system under pandemic. Their research used machine learning algorithms to categorize patients and modeled "the number of outpatients and inpatients, as well as medical waste, as a Markovian healthcare waste queueing-inventory system." This approach enabled effective management of congestion in medical centers and medical waste during the pandemic, demonstrating the importance of adaptive forecasting systems that can respond to external shocks. 
Burinskiene (2022) developed a forecasting model specifically for the pharmaceutical retail sector. Their research addressed the unique challenges of retail pharmaceutical forecasting, including seasonal variations, promotional effects, and regulatory changes. 
This work provides valuable insights into the specific forecasting requirements of retail pharmacy operations. 
Deekshitha et al. (2023) explored pharmaceutical inventory management through categorization methods and seasonal demand forecasting. Their research highlighted the importance of proper product categorization in developing effective forecasting strategies for different types of medications. This approach recognizes that different pharmaceutical products may require different forecasting methodologies based on their demand patterns and clinical importance. 
Hao (2024) investigated the integration of demand forecasting and inventory optimization for streamlining supply chain management. Their research demonstrated how "integrated approaches that combine forecasting with optimization can achieve better results than treating these processes separately" (Hao, 2024). This integrated perspective is particularly valuable in healthcare settings where forecasting must directly inform inventory decisions. 
Huang et al. (2021) applied a medical material inventory model under deep learning in supply planning for public emergencies. Their research demonstrated how "deep learning approaches can enhance emergency supply planning by better predicting demand surges during crisis situations" (Huang et al., 2021). This work highlights the importance of robust forecasting methods for emergency preparedness in healthcare settings. 
 Implementation Challenges and Organizational Factors 
The implementation of advanced forecasting systems in healthcare settings presents significant challenges beyond technical considerations. Organizational factors, workflow integration, and change management play crucial roles in the success of healthcare forecasting initiatives. 
Refonaa et al. (2023) examined the implementation challenges of medical inventory management systems, finding that "their implementation can be challenging due to various factors such as data inaccuracy, inadequate training, and lack of standardization." Their research highlighted that successful implementation requires "a comprehensive understanding of the healthcare facility's unique needs and challenges," emphasizing the importance of contextual factors in healthcare forecasting implementation. 
Data quality represents a significant challenge in healthcare forecasting. Musimbi (2022) developed a predictive analytics model for pharmaceutical inventory management and identified data quality as a critical factor affecting forecast accuracy. The research found that "ineffective drug management has a significant financial impact on pharmacies" and that predictive analytics can help ensure "that needed drugs or medicines are always available, in sufficient quantities, of the right type and quality." This research highlights the importance of addressing data quality issues to achieve accurate forecasts. 
Integration with existing healthcare information systems presents another implementation challenge. Sumathi et al. (2023) developed an appointment booking and drug inventory system in healthcare services using blockchain technology. Their research demonstrated that "compared to existing centralized storage techniques, the proposed decentralized storage technique provides higher data availability, the fastest response time, and immutable storage of existing data." This research highlights the potential of emerging technologies to address integration challenges in healthcare forecasting systems. 
Organizational readiness and change management significantly impact the success of healthcare forecasting implementations. Yawara et al. (2023) examined purchasing planning for pharmaceuticals inventory in a hospital drug warehouse, finding that implementation of forecasting methods and inventory models reduced costs by 22.15% compared to current methods. However, they emphasized that successful implementation required organizational commitment and staff training to effectively utilize the forecasting tools. 
Keskin (2022) explored digital supply chain transformation through integrated supply and demand planning via concurrent planning. Their case study of Jabil demonstrated how "organizational alignment and process integration are as important as technological capabilities in successful forecasting implementations" (Keskin, 2022). This research highlights the sociotechnical nature of forecasting system implementation. 
Kraljevic and Juanatas (2024) investigated inventory optimization through sales forecasting. Their research emphasized the importance of aligning forecasting methodologies with organizational objectives and operational constraints. This work highlights that even the most sophisticated forecasting techniques must be implemented in ways that align with organizational realities to achieve practical benefits. 
Poornima et al. (2024) explored inventory tracking via IoT in the pharmaceutical industry. Their research demonstrated how "Internet of Things technologies can enhance data collection and real-time visibility, improving the accuracy of forecasting inputs" (Poornima et al., 2024). This work highlights the importance of data collection infrastructure in supporting effective forecasting systems. 
Sakhare and Kulkarni (2022) conducted a predictive analysis of end-to-end inventory management systems for perishable goods. Their research identified implementation challenges specific to perishable products, including data integration issues, real-time monitoring requirements, and the need for responsive forecasting models. This work provides valuable insights into the practical challenges of implementing forecasting systems for time-sensitive healthcare products. 
 Privacy, Security, and Ethical Considerations 
The implementation of advanced forecasting systems in healthcare raises important privacy, security, and ethical considerations that must be addressed to ensure responsible and effective use of these technologies. 
Avraam et al. (2022) developed a deterministic approach for protecting privacy in sensitive personal data. Their research demonstrated how "privacy-preserving techniques can be applied to healthcare data while maintaining analytical utility" (Avraam et al., 2022). This work addresses a critical concern in healthcare forecasting: how to leverage sensitive patient data for improved predictions while protecting individual privacy. 
Bhattacharjee et al. (2020) developed a unified GPU technique to boost confidentiality, integrity, and trim data loss in big data transmission. Their research demonstrated how "advanced cryptographic techniques can secure healthcare data during transmission and processing" (Bhattacharjee et al., 2020). This work addresses the security challenges associated with collecting and analyzing the large datasets required for effective healthcare forecasting. 
Sievering et al. (2022) compared machine learning methods with logistic regression analysis in creating predictive models for critical in-hospital events in COVID-19 patients. Their research highlighted the ethical implications of algorithmic decision-making in healthcare, noting that "the interpretability of predictive models is an important ethical consideration when these models inform clinical decisions" (Sievering et al., 2022). This work emphasizes the importance of transparent and explainable forecasting models in healthcare settings. 
Tesfaye et al. (2024) applied machine learning methods for predicting childhood anemia using demographic health survey data. Their research demonstrated how "machine learning can extract valuable insights from population-level data to inform public health interventions" (Tesfaye et al., 2024). This work highlights the potential of forecasting techniques to address broader public health challenges while raising important questions about data representation and algorithmic fairness. 
Feretzakis et al. (2024) developed a machine learning model for medical triage, creating a predictive model for emergency department disposition. Their research demonstrated that "machine learning can effectively predict patient disposition in emergency settings, potentially improving resource allocation and patient flow" (Feretzakis et al., 2024). This work highlights the potential of predictive analytics to enhance operational efficiency in healthcare settings while raising important questions about the role of algorithmic decision support in clinical environments. 
Korkmaz et al. (2024) enhanced firewall packet classification through artificial neural networks and synthetic minority over-sampling techniques. While focused on cybersecurity rather than healthcare specifically, their methodological innovations in handling imbalanced data have significant implications for healthcare forecasting, where rare events (such as disease outbreaks or unusual demand patterns) often represent the most critical prediction targets. 
Emerging Technologies  
The integration of emerging technologies with healthcare forecasting systems represents a promising frontier for further advancements in inventory management and demand 
prediction. 
Brancato et al. (2024) explored the standardization of digital biobanks through the integration of imaging, genomic, and clinical data for precision medicine. Their research demonstrated that "harmonizing these diverse data modalities enables more granular patient stratification and treatment pathway prediction" (Brancato et al., 2024). This capability has profound implications for inventory management, potentially enabling medication forecasting at the molecular subtype level rather than broad diagnostic categories. 
Iannantuono et al. (2023) examined the applications of large language models in cancer care, analyzing current evidence and future perspectives. Their research highlighted how "large language models can enhance clinical decision-making, improve patient education, streamline documentation, and accelerate research" (Iannantuono et al., 2023). These capabilities have significant implications for healthcare forecasting, potentially enabling more sophisticated analysis of unstructured clinical data to inform demand predictions. 
Kraljevic et al. (2024) explored large language models for medical forecasting in their work on Foresight . Their research demonstrated the potential of language models to "incorporate contextual information and domain knowledge into forecasting processes, potentially improving prediction accuracy for complex healthcare scenarios" (Kraljevic et al., 2024). This work represents an emerging frontier in healthcare forecasting, combining the pattern recognition capabilities of traditional forecasting methods with the contextual understanding of language models. 
Verma (2024) investigated transforming supply chains through AI, focusing on demand forecasting, inventory management, and dynamic optimization. Their research highlighted how "artificial intelligence can enable more responsive and adaptive supply chain management through continuous learning and optimization" (Verma, 2024). This work points to a future where healthcare forecasting systems can dynamically adapt to changing conditions and continuously improve their predictive accuracy. 
Subramanian (2021) examined effective demand forecasting in health supply chains, identifying emerging trends, enablers, and blockers. Their research provided a comprehensive overview of the evolving landscape of healthcare forecasting, highlighting both technological advancements and organizational factors that influence forecasting effectiveness. This work offered valuable insights into the trajectory of healthcare forecasting and the factors that shaped its future development. 
Sibindi et al. (2023) developed a boosting ensemble learning-based hybrid light gradient boosting machine and extreme gradient boosting model for predictive analytics. While their application focused on house price prediction rather than healthcare, their methodological innovations in ensemble learning have significant implications for healthcare forecasting. Their research demonstrated that "hybrid approaches that combine multiple boosting algorithms can achieve superior predictive performance compared to single-algorithm approaches" (Sibindi et al., 2023). 
Srilakshmi et al. (2024) proposed a new approach to computationally-successful linear and polynomial regression analytics of large data in medicine. Their research demonstrated how "traditional regression techniques can be enhanced through computational 
optimizations to handle the large datasets typical in healthcare settings" (Srilakshmi et al., 2024). This work highlights the continuing relevance of foundational statistical methods alongside newer machine learning approaches in healthcare forecasting. 
Sowmiya (2024) developed a multifactorial approach for predicting shelf-life of fruits using intrinsic and extrinsic determinants. While focused on food rather than pharmaceuticals, their methodological approach to perishability prediction has significant implications for healthcare inventory management, particularly for time-sensitive medications and biological products. 
Future Directions 
Despite significant advancements in healthcare forecasting, several important research gaps remain. Identifying these gaps provides direction for future research and highlights opportunities for further improvement in healthcare inventory management. 
First, there is limited research on the adaptation of advanced forecasting techniques for resource-constrained healthcare settings. Most studies focus on well-resourced facilities with robust data infrastructure, creating a knowledge gap regarding the feasibility and performance of these methods in diverse healthcare contexts. Pantha (2023) addressed this gap by examining demand prediction and inventory management of surgical supplies in resource-limited settings, but more research is needed to develop forecasting approaches suitable for diverse healthcare environments. 
Second, the integration of multiple data modalities in healthcare forecasting remains underdeveloped. While individual studies have demonstrated the value of clinical, demographic, or environmental data, few have successfully integrated these perspectives into comprehensive forecasting frameworks. Muhamediyeva et al. (2024) explored forecasting the market needs for medicines based on artificial intelligence technologies by incorporating multiple data sources, but further research is needed to develop integrated approaches that capture the complex interplay of factors affecting medication utilization. 
Third, longitudinal evaluation of forecasting system sustainability is notably absent from the literature. Most studies report short-term performance metrics without examining how these systems adapt to evolving healthcare needs or changing practice patterns. This temporal gap limits understanding of how forecasting systems should be designed for longterm effectiveness rather than merely initial performance. 
Fourth, ethical frameworks specifically designed for algorithmic inventory management in healthcare remain underdeveloped. While general principles for healthcare AI exist, their application to inventory management raises unique considerations regarding resource allocation and access equity. This ethical gap requires dedicated attention to ensure that increasingly autonomous inventory systems align with healthcare values and equitably serve diverse patient populations. 
Fifth, the integration of blockchain technology with healthcare inventory management represents an emerging area with significant potential. Sumathi et al. (2023) demonstrated that blockchain can provide "a transparent and tamper-resistant medical inventory to prevent the unauthorized sale of medicines and drugs and also verify the availability of the drug." However, more research is needed to fully explore the potential of blockchain and other distributed ledger technologies in healthcare forecasting and inventory management. 
Sixth, the application of agile methodologies to healthcare forecasting system development represents an underexplored area. Russo (2021) examined the agile success model through a mixed-methods study of a large-scale agile transformation. Their research provided insights into how "agile approaches can enhance the responsiveness and adaptability of complex system implementations" (Russo, 2021). Applying these principles to healthcare forecasting system development could potentially address some of the implementation challenges identified in the literature. 
 
 
 
 
 
Synthesis  
The reviewed literature reveals a growing body of research focused on the application of predictive analytics and machine learning in healthcare inventory management. Huang et al. (2021) demonstrated how deep learning models can improve demand forecasting during emergencies, emphasizing the importance of accurate prediction models in critical healthcare scenarios. Similarly, studies by Musimbi (2022) and Sakhare and Kulkarni (2022) confirmed that predictive analytics can enhance pharmaceutical inventory accuracy, reduce waste, and ensure medicine availability. These findings underscore the potential of time series and machine learning models—such as ARIMA, Holt-Winters, and gradient boosting—in improving forecasting capabilities in healthcare systems. 
However, technical solutions alone are not sufficient. Multiple studies highlight implementation challenges that involve organizational, operational, and data-related factors. Refonaa et al. (2023) and Yawara et al. (2023) identified issues such as data inaccuracy, limited staff training, and poor integration into existing workflows as major barriers to effective adoption. This is supported by Keskin (2022), who emphasized that organizational alignment and stakeholder readiness are equally important as the forecasting tools themselves. Integration challenges were also addressed by Sumathi et al. (2023), who proposed blockchain as a solution for improving data availability and security, while Poornima et al. (2024) showed how IoT-based systems can enhance real-time visibility of inventory data. 
Beyond implementation, ethical, privacy, and security concerns also emerge as critical considerations. Studies by Avraam et al. (2022) and Bhattacharjee et al. (2020) explored data privacy and encryption techniques necessary for safeguarding sensitive health data. Meanwhile, Sievering et al. (2022) and Tesfaye et al. (2024) stressed the importance of transparency, fairness, and interpretability in AI-driven models used for forecasting and decision-making in healthcare. 
The literature also reflects the impact of emerging technologies. Researchers like Brancato et al. (2024) and Iannantuono et al. (2023) explored the use of large language models and multi-modal data to enhance healthcare forecasting, pointing to the future potential of integrating clinical narratives and genomic data into predictive systems. Verma (2024) and Sibindi et al. (2023) demonstrated how ensemble learning and artificial intelligence can enable more responsive and adaptive supply chain systems, while Srilakshmi et al. (2024) showed that computational enhancements of traditional regression models remain relevant in handling large healthcare datasets. 
Despite these advancements, several research gaps remain. Limited research exists on forecasting systems in resource-constrained settings, as noted by Pantha (2023). There is also a lack of longitudinal studies evaluating the sustainability and adaptability of forecasting systems over time. Furthermore, ethical frameworks specific to algorithmic inventory management and the full potential of blockchain and agile methodologies in healthcare forecasting are still underexplored. 
Overall, the literature supports the development of systems like BMC MedForecast, which addresses the identified gaps by integrating intelligent forecasting, recommender systems, and robust implementation strategies within a healthcare-specific context. The system not only leverages modern predictive algorithms but also considers practical, ethical, and organizational dimensions critical for its success and sustainability. 
Significance of the Study 
This study endeavors to pioneer an AI-driven approach to medicine management at the Bulacan Medical Center’s Pharmacy and Supply Department. With the approach of harnessing forecasting techniques, the research aims to optimize inventory control, enhance supply chain efficiency, and ensure timely medication availability. The development of this system is crucial for addressing seasonal fluctuations and quarterly demand patterns, thereby improving responsiveness to patient needs while minimizing medication wastage. 
Provincial Government of Bulacan. The findings from this study provided a blueprint for the Provincial Government of Bulacan to revolutionize medicine management practices. By implementing the AI-driven process, the government can expect improved resource allocation, reduced operational costs, and enhanced healthcare service delivery. 
Bulacan Medical Center Management. Hospital administrators such as department heads and admin officers benefited from streamlined inventory management processes facilitated by AI. This includes real-time monitoring of stock levels, automated replenishment based on predictive models, and better supplier management. Such advancements lead to optimized resource utilization and a more agile response to patient care demands. 
Medical Center Pharmacy and Supply Department. Staff within the Pharmacy and Supply Department experienced enhanced workflow efficiency through automated inventory reporting and optimized stock rotation. This system reduced the incidence of stockouts, minimize medication wastage, and ensure continuous availability of essential medicines, thereby improving overall patient care outcomes. 
Future Research and Development. This study serves as a pioneering initiative in AIdriven medicine management within healthcare institutions. Future researchers can build upon this foundation to explore further applications of AI in healthcare logistics and supply chain management. The findings contributed valuable insights for advancing pharmaceutical inventory management practices globally, promoting continuous innovation in healthcare operations. 
 
 
 
 
 
 
Theoretical Framework/Conceptual Framework of the Study  
 
Table 1. Conceptual Model of the Study 
 
 
 
 
 
 
The BMC MedForecast: Inventory Optimization and Monitoring Management System is a data-driven solution designed to enhance the efficiency of medicine inventory management through the use of advanced forecasting algorithms. Anchored in the Input– Process–Output (IPO) model, the system integrates multiple data sources, analytical methods, and software tools to deliver accurate and timely inventory insights. The input phase involves the collection of critical datasets, including medicine delivery records from the General Services of the Provincial Government of Bataan (PGB), issuance of medicines from pharmacy departments, sales data representing outgoing medicines, medicine expiry data, and classification information. These are supported by technological resources essential for processing and analysis. The process phase is centered on the development and deployment of an intelligent forecasting engine built using the Django web framework and Python programming language. A wide range of forecasting and machine learning techniques are integrated into the system, including ensemble methods, time series analysis, and statistical modeling. Among the specific methods used are Gradient Boosting, 
ARIMA models, Holt-Winters Exponential Smoothing, Moving Average calculations, and Linear and Polynomial Regression models. These tools work in tandem to form the system’s forecasting and recommender modules, which analyze historical and real-time data to generate adaptive predictions and optimize inventory levels. Through this process, BMC MedForecast aims to minimize stock-outs, prevent overstocking, and reduce medicine wastage, ultimately improving the delivery of healthcare services. 
 
 
Statement of the Problem   General Statement of the Problem 
The integration of modern inventory management trends and technological innovations in healthcare facilities, such as the Bulacan Medical Center, is crucial for enhancing operational efficiency, improving patient care, and addressing the unique challenges faced by rural medical institutions. This research initiative aims to explore and implement advanced digital solutions to transform the inventory management processes at the Bulacan Medical Center, ensuring optimal medication supply and enhancing overall healthcare delivery. 
Specifically, the researcher aims to answer the following research questions 
What are the existing methods employed by Bulacan Medical Center for managing medicine supplies? 
What are the salient features of the proposed system? 
How can the Decision Support system can be integrated using the forecasting technique in terms of medical inventory? 
How acceptable the system as perceived by respondents and experts based on ISO/IEC 
25010 standards in terms of 
Functional completeness 
Functional correctness 
Functional appropriateness 
Performance efficiency 
Compatibility 
Usability 
Reliability 
Security 
      Definition of Terms 
This study presents essential terms for understanding the integration of modern inventory management trends and technological innovations in the Bulacan Medical Center's Pharmacy and Supply Department. These definitions provide concise conceptual and operational explanations of key terms. 
Artificial Intelligence (AI) - AI refers to the simulation of human intelligence processes by machines, especially computer systems. In this study, AI algorithms are used to analyze historical data, predict medicine demand, and optimize inventory levels. 
Electronic Databases - Electronic databases are digital systems used to store and manage data electronically. In the context of this study, they facilitate real-time inventory tracking, ensuring timely replenishment of medicine supplies. 
Internet of Things (IoT) - IoT refers to a network of physical objects embedded with sensors, software, and other technologies to connect and exchange data with other devices and systems over the internet. In this study, IoT devices enable real-time monitoring of medicine inventory. 
Machine Learning (ML) - ML is a subset of AI that involves the use of algorithms and statistical models to enable computers to improve their performance on a task through experience. In this study, ML models predict future medication demand based on historical data. 
Predictive Analytics - Predictive analytics involves using historical data, statistical algorithms, and machine learning techniques to predict future outcomes. This study uses predictive analytics to forecast medicine demand and optimize inventory management. Radio Frequency Identification (RFID) - RFID technology uses electromagnetic fields to automatically identify and track tags attached to objects. In the context of this study, RFID tags on medicine packages provide real-time data on inventory levels and expiration dates. 
Real-Time Monitoring Systems - Real-time monitoring systems use sensors and IoT devices to continuously track and report on inventory levels and usage. These systems enable healthcare staff to receive alerts for low stock levels or expired medications, ensuring timely replenishment. 
     Scope and Delimitation of the Study 
This study aims to develop a forecasting of medicine for inventory support process management, enhancing the efficiency of medicine requests based on seasonal variations and quarterly demand patterns using forecasting algorithm. 
Conducted exclusively within the Provincial Government of Bulacan's Medical Center’s Pharmacy Department, the research focuses on leveraging forecasting methods to forecast medicine to purchase according to stocks from historical data, generate inventory reports, monitor inventory levels of medicine, the study aims to create a strong system for sustainable inventory optimization of medicine management, the study collected and 

 	 	    33 
 
analyze historical data on inventory levels and sales trends  specific to the Bulacan Medical Center. 
The study is limited to the Bulacan Medical Center’s Pharmacy Department and not include other medical supplies or equipment, focusing solely on medicine management. The research is confined to this geographical location and does not cover traditional inventory management methods without AI integration. Additionally, the study concentrates on the development and planning phase, excluding the full-scale implementation or postimplementation evaluation.
 
 	32 
 
Chapter 2 
Methodology of the Study 
 
This chapter presents the methods and techniques, respondents of the study, instrument of the study, the data processing and statistical treatment applied and ethical considerations. 
Methods and Techniques 
The researcher used a descriptive and developmental type of researcher.   The developmental type of researcher enables the researcher to develop the system entitled 
BMC MedForecast: An Inventory Optimization Monitoring and Management System using Forecasting Algorithms. The researcher used the prototyping development model as the main methodology as it provides advantages as the researcher can start the system even if the requirements are not yet fully defined. 
In terms of the quantitative research, the researcher used a validated instrument 
Collect structured data on expert perceptions and attitudes towards the new inventory management system. By using standardized questionnaires and Likert scales, the researcher can quantify responses and identify patterns or trends across respondents. 
Prototyping Model 
Prototyping serves as an incremental approach to software development, designed to facilitate progress even in the absence of complete requirements. Its primary objective is to iteratively refine the software until it aligns with the desired functional specifications. 
Prototyping Diagram 
Step 1: Requirements Gathering and Analysis. A requirement analysis is the first step in a prototyping model. The system's requirements are defined in depth during this phase. Throughout the process, users of the system are surveyed to learn what they expect from 
it. 
Step 2: Quick Design. Following the meticulous gathering and analysis of requirements, the subsequent phase involves the creation of a preliminary design, often referred to as rapid design. This stage entails the development of a foundational system design, providing stakeholders with a swift yet comprehensive overview of the envisioned system. The researchers have described analytical systems in terms of what the system must do to meet the user's information needs.  It also influences how the system accomplishes its aims and goals. 
Step 3: Build a Prototype. The information acquired during quick design is used to create an actual prototype in this phase. It's a scaled-down version of the required system. In the coding step, Python Django Framework and MySQLite Database are used for the development of the proposed system 
Forecasting and Time Series Analysis 
Time Series Analysis: Using time series-forecasting techniques enables you to predict inventory needs based on past consumption patterns. Time series analysis helps identify seasonal variations, trends, and cyclicality in demand, allowing for proactive inventory management.   
Step 4: Initial User Evaluation. The proposed system is given for an initial review at this step. It aids in determining the working model's strengths and weaknesses. Customer 

feedback and suggestions are gathered and forwarded to the developer.  The researcher conducts an online survey that evaluates the system by IT Experts. 
Step 5: Refining Prototype. If the user is dissatisfied with the present prototype, you must improve it based on the user's feedback and suggestions. This step continue until all of the user's criteria have been met. A final system is produced based on the approved final prototype once the user is pleased with the developed prototype. 
Step 6: Implement Product and Maintain. The final system is thoroughly tested and deployed to production when it is developed based on the final prototype. Regular maintenance is performed on the system to save delays and prevent major failures. The researchers spent this phase creating complete documentation of the operation and performing other necessary maintenance. The problem has been identified and corrected. 
 
 
 
Figure 2. Prototyping Model 
  
 
 
Figure 3. Use Case Diagram 
 
 
 
 
 
Figure 4. System Architecture Diagram 
 
Respondents of the study 
  The respondents of the study were the Bulacan Medical Center pharmacy and Information Technology experts.  They are the needed respondents and chosen because they are knowledgeable of the current process inventory and monitoring of the products and its corresponding management and they were responsible for testing and using the system.   
The table below indicates the group of respondents who assessed the developed system.  
The group of respondents consist of IT Experts and BMC practitioners.  
 
 
 
Respondents 
	IT Experts 	10 	 IT professionals who had involvement in 
systems development projects 
  
  
Table 2 Respondents 
 
 Instrument of the Study 
In terms of software evaluation, the researcher used  ISO/IEC 25010 standards. The properties of software can be assessed using the characteristics of the said quality model, which includes functional suitability, performance efficiency, compatibility, usability, reusability, security, maintainability, and portability. Respondents who were categorized as 	experts 	using 	these 	characteristics 	evaluated 	the 	system 
 
Figure 4.0 ISO IEC 25010 Model 
 
  To measure the system in a holistic approach, it is necessary that a working system be evaluated by a certain standard in software development. As gleaned in the table above, the ISO/IEC 25010 consists of the following criteria, which include functional suitability, reliability, performance and efficiency, operability, security, compatibility, maintainability and transferability.  Each criterion is provided with sets of indicators to measure the perceptions and evaluation of the respondents. SO/IEC 25010 is a standard that defines a model for the quality of software products. The ISO/IEC 25010 model focuses on software quality characteristics and sub-characteristics, providing a framework for evaluating and specifying the quality requirements of software products. 
The model defines eight main quality characteristics: 
Functional Suitability: This refers to the degree to which the software satisfies specified functional requirements. 
Performance Efficiency: It's about the performance relative to the amount of resources used under stated conditions. This includes aspects like response time, throughput, and resource utilization. 
Compatibility: This involves the software's ability to coexist with other software in a common environment, sharing resources without interference. 
Usability: It evaluates how easy the software is to use, understand, and operate for the intended users. 
Reliability: This characteristic is about the software's ability to maintain a specified level of performance when used under specified conditions for a specified period. 
Security: It refers to the software's ability to protect data and maintain functionality under stated security threats. 
Maintainability: This characteristic evaluates the effort required to make modifications to the software, including corrections, improvements, or adaptations. 
Portability: It assesses the ability of the software to be transferred from one environment to another. 
The use of surveys identified the status of the system by determining if the system met the required requirements which were given by the Pharmacy staff. The process helped determine if the system was sufficient or if there were changes needed for the development and improvement of the system, which helped them minimize their tedious jobs, generate reports with ease, and hasten the procedures on their pharmacy. This survey was distributed online using Google forms. In this way, the survey was accessible to the respondents even when they were far distant from the researcher. The survey was based on the questionnaires from ISO 25010. 
Sampling Technique 
In this section, the researcher utilized a non-probabilistic sampling approach known as Purposive Sampling. This technique, also referred to as judgment sampling or authoritative sampling, involved the deliberate selection of sample units based on the researcher's existing knowledge or professional judgment. 
Data Gathering Procedures 
 The system underwent the required procedures in order for the system to develop, and the data needed to be gathered came from the respondents. Series of meetings, interviews, clarifications, observations, and content analysis were done. As the software was developed through prototyping, data gathering, and development were almost simultaneous. A prototype was developed according to the inputs of the respondents. A cycle of data gathering-development-testing was established until the system was polished and ready for deployment. In this way, the development was faster because clarifications and inputs were available from the respondents. As for the efficiency of the system, data from the system findings collection program were used together with the online survey that was given to the respondents. 
Data Processing and Statistical Treatment 
  The main goal of the system was to create a smooth inventory management that predicted the medicines that were needed to acquire during the quarters and different seasons. The process of utilizing and generating the report was lessened and the timeconsuming encoding of the report was processed faster, and the transactions for the sales of the said client were efficient. However, the study determined the effectiveness of the system through descriptive analysis regarding how the system helped the staff and how the system was able to hasten the processing. Statistical analysis was used to determine how many findings or concerns the system had starting from its first day of implementation. The primary mode in gathering data was through the floating of questionnaires to respondents via electronic means. The respondents were given questions based from the constructs of the Software Quality Model. In gathering the above-mentioned data, the researcher undertook the following procedures: 
 
The researcher sought permission from the Head of the Organization  before conducting the study. 
With the approval of the Head, the researcher administered the distribution of the questionnaires to the respondents thru Google Form. 
To observe health and safety protocols, the researcher utilized the google form interactions with the respondents.  
Statistical Treatment of the Data 
       To describe the evaluation of the respondents using the ISO 250010 criteria; the following are the statistical methods to be used in answering some of the research questions. 
       	Percentage 
       	Formula 1  	P= f/N x 100 
       	Where: 
       	f= frequency 
       	N= number of respondents 
       	P= percentage 
To describe the perceptions of the users using the proposed criteria mean performance was used. 
       	Mean 
       	Mean is calculated using this formula: 
                 	WM= fw1 + fw2 + fw3 
                                   N 
       	Where 
                 	WM 	= weighted mean                  	fw    	= frequency x weight 
  	N     	= number of respondents 
 
Ethical Compliance 
   	The researcher was very careful with the ethical standards which were needed in creating and gathering data from different users with all ethical consideration of the medicine and its partners such as: 
The respondents of the study were treated with respect and their certain data were not subjected to harm or any malicious intent. 
The data of the respondents was the biggest priority by protecting the privacy of the respondents 
The data also of the said organization was being subject to protection. 
The BMC was well informed with the study that was being conducted. 
The members of the respondents were well informed of the study. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Chapter 3 
Presentation, Analysis, and Interpretation of Data 
This chapter presents the results, analysis, and interpretation of the data collected, while also addressing the research questions formulated in the study. The data is presented in both tabular and textual formats, supported by appropriate statistical methods. The primary aim of this section is to provide a comprehensive discussion of the findings in relation to the research objectives. The study centers on the BMC MedForecast: Inventory Optimization and Monitoring Management System. By integrating forecasting algorithms with modern inventory management practices and technological innovations, healthcare facilities—such as Bulacan Medical Center—can significantly enhance operational efficiency, improve patient care, and address the unique challenges commonly faced by rural medical institutions. 
1.0  Existing Methods employed by Bulacan Medical Center in Terms of Medicine Supply management  
The current medicine supply management system at Bulacan Medical Center (BMC) employs a threshold-based inventory control methodology that relies primarily on predetermined reorder points to trigger procurement activities. As illustrated in the process flowchart, this approach follows a reactive decision-making framework rather than a proactive forecasting model. 
Operational Workflow Analysis 
The medicine supply management process at BMC follows a sequential workflow that begins with inventory verification and concludes with stock level updates. The process initiates with a manual inventory check where staff physically assess current medicine quantities. When stock levels fall below established reorder thresholds, the system triggers a purchase order creation process. This binary decision point (stock below reorder level: yes/no) represents a fundamental limitation of the current system, as it lacks nuanced consideration of demand variability, seasonal fluctuations, or consumption trends. 
Following purchase order creation, the process continues through medicine receipt and inventory record updates. This linear workflow, while providing basic inventory control, demonstrates significant limitations in terms of demand anticipation and inventory optimization. The process terminates after inventory updates without incorporating analytical feedback loops that could inform future procurement decisions. 
Reorder Level System Evaluation 
The reorder level system, as depicted in the graph, employs three critical threshold values: reorder level (approximately 25 units), maximum stock level (approximately 100 units), and current inventory levels (fluctuating between 80-110 units over the six-month period). This system establishes a simplified inventory control mechanism where procurement activities are initiated when inventory reaches the reorder point (indicated by the red circle in March). 
 
Analysis of the inventory level trajectory reveals several operational inefficiencies. The inventory level exceeds the maximum stock threshold in April (yellow circle), indicating potential overordering and inefficient capital utilization. Conversely, the sharp decline in inventory levels between April and May suggests potential stockout risks if procurement lead times had been extended. The subsequent recovery in June demonstrates the reactive nature of the current system, responding to inventory depletion rather than anticipating it. 
 
The consistent gap between the reorder level and actual inventory levels throughout most of the observation period suggests that while the system prevents complete stockouts, it maintains unnecessarily high inventory levels that tie up financial resources and increase carrying costs. This approach fails to optimize the balance between inventory investment and medicine availability. 
Methodological Limitations 
The current methodology exhibits several significant limitations that impact operational efficiency and resource utilization: 
Absence of Demand Forecasting: The system operates without statistical forecasting capabilities, relying instead on historical reorder points that remain static regardless of changing demand patterns. This absence of predictive analytics prevents anticipatory inventory management. 
Manual Intervention Dependencies: Inventory checks and procurement decisions require substantial manual intervention, introducing potential delays and human error into the supply chain process. The lack of automation in threshold monitoring increases operational overhead. 
Uniform Treatment of Medicines: The current system applies similar inventory management principles across all medicines without differentiation based on criticality, demand variability, or value. This one-size-fits-all approach fails to prioritize resources appropriately. 
Limited Data Utilization: Despite collecting transaction data, the system demonstrates minimal analytical capabilities for identifying consumption trends, seasonal variations, or correlation with disease prevalence patterns. This represents a significant missed opportunity for data-driven decision making. 
Reactive Rather Than Proactive Orientation: The fundamental design philosophy emphasizes reaction to inventory depletion rather than proactive management based on anticipated needs. This reactive stance increases the risk of both stockouts and overstocking. 
Implications for Healthcare Delivery 
The limitations of the current medicine supply management system have direct implications for healthcare service delivery at BMC. The reactive inventory management approach increases the risk of medicine unavailability, potentially compromising patient care quality. Simultaneously, the tendency toward overstocking to prevent stockouts results in inefficient capital allocation that could otherwise support other healthcare priorities. 
The absence of sophisticated forecasting capabilities particularly impacts management of medicines with seasonal demand patterns or those associated with communicable disease outbreaks. Without the ability to anticipate demand surges, the medical center remains vulnerable to supply-demand misalignments during critical periods. 
Furthermore, the manual nature of many inventory management processes diverts healthcare staff time from patient care activities to administrative inventory management tasks, representing an opportunity cost in terms of clinical service delivery. 
 
  
 	 	 	 	 	Figure 4. Flowchart Process 
 
 
Figure 5. Reorder level system 
The current medicine supply management system at Bulacan Medical Center (BMC) employs a threshold-based inventory control methodology that relies primarily on predetermined reorder points to trigger procurement activities. As illustrated in the process flowchart, this approach follows a reactive decision-making framework rather than a proactive forecasting model. 
Operational Workflow Analysis 
The medicine supply management process at BMC follows a sequential workflow that begins with inventory verification and concludes with stock level updates. The process initiates with a manual inventory check where staff physically assess current medicine quantities. When stock levels fall below established reorder thresholds, the system triggers a purchase order creation process. This binary decision point (stock below reorder level: yes/no) represents a fundamental limitation of the current system, as it lacks nuanced consideration of demand variability, seasonal fluctuations, or consumption trends. 
Following purchase order creation, the process continues through medicine receipt and inventory record updates. This linear workflow, while providing basic inventory control, demonstrates significant limitations in terms of demand anticipation and inventory optimization. The process terminates after inventory updates without incorporating analytical feedback loops that could inform future procurement decisions. 
Reorder Level System Evaluation 
The reorder level system, as depicted in the graph, employs three critical threshold values: reorder level (approximately 25 units), maximum stock level (approximately 100 units), and current inventory levels (fluctuating between 80-110 units over the six-month period). This system establishes a simplified inventory control mechanism where procurement activities are initiated when inventory reaches the reorder point (indicated by the red circle in March). 
Analysis of the inventory level trajectory reveals several operational inefficiencies. The inventory level exceeds the maximum stock threshold in April (yellow circle), indicating potential overordering and inefficient capital utilization. Conversely, the sharp decline in inventory levels between April and May suggests potential stockout risks if procurement lead times had been extended. The subsequent recovery in June demonstrates the reactive nature of the current system, responding to inventory depletion rather than anticipating it. 
The consistent gap between the reorder level and actual inventory levels throughout most of the observation period suggests that while the system prevents complete stockouts, it maintains unnecessarily high inventory levels that tie up financial resources and increase carrying costs. This approach fails to optimize the balance between inventory investment and medicine availability. 
 
2.0 Salient Features of the System   
 
 	 	 	 	Figure 6. Medicine Create Transactions 
The template represents a critical operational interface within the BMC Medicine Forecasting System, enabling staff to record medicine movements with precision and efficiency. This carefully designed page transforms the potentially complex process of transaction recording into a streamlined workflow that supports accurate inventory management. 
When staff access the transaction creation page, they encounter an intuitive form-based interface organized into logical sections that guide them through the transaction recording process. The page opens with a transaction type selection (typically purchase or sale) that dynamically adjusts the subsequent form fields to capture only relevant information for the selected transaction type. 
What distinguishes this page is its intelligent medicine selection system. Staff can locate specific medicines through multiple approaches: typing partial names in a search field with real-time suggestions, scanning barcodes using connected scanners, or browsing categorized medicine lists. Once a medicine is selected, the system automatically populates current inventory information including available quantity, unit price, and expiration dates for the selected item. This immediate context helps staff make informed decisions about appropriate transaction quantities. 
For sales transactions, the page includes a customer information section where staff can either select returning customers from a searchable dropdown or enter information for new customers. The customer search functionality works similarly to the medicine search, offering real-time suggestions as staff type. When selecting returning customers, the system displays their recent transaction history, helping identify potential medication overuse patterns or frequent purchases that might require clinical attention. 
The quantity field includes intelligent validation that prevents common errors. For sales transactions, the system prevents recording quantities exceeding available stock, while for all transactions, it flags unusually large quantities that might indicate data entry errors. The system also calculates and displays the transaction's total value in real-time as quantities or prices change. 
For purchase transactions, the page includes fields for supplier information, invoice numbers, and expected delivery dates. The system maintains a list of frequent suppliers for quick selection while allowing new supplier information to be entered when needed. 
The page incorporates several safety features that support proper medication dispensing. For prescription medications, the system includes optional fields for prescription information and prescribing physician. When dispensing controlled substances, additional verification fields appear to ensure regulatory compliance. The system also displays warnings for potential overdose situations based on the customer's recent purchase history. For batch-tracked medicines, the interface allows staff to select specific batches and expiration dates, supporting FEFO (First Expired, First Out) inventory management. The system prioritizes suggesting batches closest to expiration to minimize waste. 
Before final submission, the page displays a transaction summary for verification. Upon submission, the system provides immediate confirmation that the transaction was recorded successfully, including its impact on current inventory levels. For sales transactions, the page offers options to print or email receipts directly from the confirmation screen. 
The entire interface adapts responsively to different screen sizes, with special consideration for mobile layouts that might be used in warehouse or pharmacy settings where desktop computers aren't always accessible. Field sizes, button placements, and form organization are optimized for both precision and speed, recognizing that transaction recording is a frequent, high-volume activity in busy healthcare settings. 
This transaction creation implementation balances comprehensiveness with efficiency, capturing all necessary data for proper inventory management while streamlining the process to support the operational tempo of healthcare facilities. The thoughtful design reduces errors, speeds up data entry, and ensures accurate inventory records that form the foundation of the forecasting system's predictive capabilities. 
 
Figure 7. Inventory List 
 
The template serves as the central inventory management hub within the BMC Medicine Forecasting System, providing a comprehensive view of all medicines currently stocked at the facility. This meticulously crafted page transforms complex inventory data into an accessible, actionable format that supports efficient medicine management. 
When staff navigate to the inventory list, they encounter a well-organized tabular interface that presents the complete medicine inventory with remarkable clarity. The page opens with a summary dashboard showing key metrics: total inventory count, total inventory value, number of low-stock items, and number of items approaching expiration. This immediate overview establishes context before staff dive into individual inventory records. What makes this page particularly effective is its thoughtful organization of essential inventory information. Each medicine entry displays critical data in a logical sequence: name, current quantity, reorder level, category, unit price, total value, and last transaction date. The system uses visual indicators to highlight inventory status – items below reorder level appear with warning indicators, while out-of-stock items receive more prominent alerts. Medicines approaching expiration dates are similarly highlighted with color-coded indicators based on urgency. 
The filtering system represents one of the page's most practical features. Staff can quickly narrow down hundreds or thousands of inventory items using multiple criteria simultaneously: medicine name, category, stock status (all, low stock, out of stock), expiration status, or value range. This filtering capability transforms what could be an overwhelming inventory list into precisely the information needed for specific operational questions. 
The page incorporates a responsive data table that adapts to different screen sizes without sacrificing information density. On mobile devices, less critical columns automatically collapse into expandable details, ensuring the most important information remains visible while still allowing access to complete records when needed. 
For each medicine, the system provides context-sensitive action buttons that adapt based on the item's status. Low-stock items show prominent "Reorder" buttons that pre-populate purchase transaction forms, while all items offer options to view details, transaction history, or forecast information. This action-oriented approach helps staff quickly respond to inventory situations that require attention. 
The inventory list includes batch tracking information for medicines where applicable, showing quantity breakdowns by expiration date. This supports proper rotation of stock and helps prevent expiration waste through visual indicators of approaching expiration dates. 
A unique feature of the inventory list is its integration with the forecasting system. Each medicine entry includes a small sparkline chart showing recent consumption trends and forecast direction, providing immediate visual cues about expected future demand without requiring navigation to detailed forecast pages. Items with unusual forecast patterns receive special indicators, helping staff identify potential supply challenges before they become 
critical. 
The page also includes a batch operations section that allows authorized staff to perform actions on multiple inventory items simultaneously, such as category reassignment, batch price updates, or generating reorder lists for multiple low-stock items. 
 
 

Figure 8. Forecast List 
The forecast list page in the BMC Medicine Forecasting System serves as comprehensive forecasting command center, giving a complete overview of all medicine demand predictions in one organized view. When you navigate to this page, you'll immediately notice how it presents forecast data in a clean, structured format that makes it easy to understand future medicine needs at a glance. The page displays a sortable, filterable table of all medicines with their corresponding forecast information, including predicted demand quantities, confidence intervals, and accuracy metrics for each item. 
What makes this page particularly valuable is how it clearly shows which forecasting method was automatically selected for each medicine based on its unique demand pattern. It can instantly see which items are using the Gradient Boosting algorithm (our most advanced method), and which are using other approaches like ARIMA, Holtwinters, or Moving Average. The system intelligently selects the best algorithm for each medicine based on which one provides the highest accuracy for that specific item's demand pattern. 
The forecast list includes helpful visual indicators that make it easy to identify which items need your attention - medicines with unusual forecast patterns or potential stock issues are highlighted, while a color-coded accuracy system helps you quickly gauge the reliability of each prediction. It can sort the entire list by forecast accuracy, predicted quantity, or medicine name, allowing you to focus on the most critical items first. 
Each medicine entry includes a direct link to its detailed forecast page, where can explore the prediction in greater depth. The system automatically refreshes these forecasts daily at 7 PM, ensuring always have the most current predictions without needing to manually trigger updates. This forecast list serves as daily planning tool, giving the confidence to make informed inventory decisions. 
 
 	 	 	 	Figure 10. Medicine Forecast Detail 
 
The medicine detail template serves as a comprehensive information hub for individual medicines within the BMC Medicine Forecasting System. This page presents detailed information about a specific medicine in a structured, easy-to-navigate format that supports informed inventory management decisions. 
Upon accessing a medicine's detail page, healthcare staff are presented with a wellorganized layout that includes critical information about the selected medicine. The page header displays the medicine name prominently, accompanied by its category classification and current stock status. Below this, the page is divided into several key information sections that provide a complete picture of the medicine's profile. 
The inventory section shows current quantity, reorder level, and reorder quantity, with visual indicators that clearly highlight when stock levels are approaching or below the reorder threshold. This section also includes expiration date information with automatic warnings when medicines are nearing expiration, helping prevent waste and ensure patient safety. 
The financial section displays pricing information, including purchase price, selling price, and profit margin calculations. Historical transaction data is presented in a chronological table, allowing staff to review past purchases and sales of this specific medicine. This transaction history provides valuable context for understanding usage patterns. 
What distinguishes this page is the dedicated forecasting section, which shows demand predictions specifically for this medicine. The forecast is presented both numerically and graphically, with a line chart displaying historical demand alongside predicted future requirements. The system clearly indicates which forecasting algorithm was automatically selected for this particular medicine based on its unique demand pattern, whether that's Gradient Boosting, ARIMA, Holtwinters, Moving Average, or another method. 
The forecast visualization includes confidence intervals that show the range of possible demand scenarios, helping staff understand both the prediction and its level of certainty. Accuracy metrics for this specific medicine's forecast are displayed prominently, allowing staff to gauge the reliability of the prediction. 
The page also includes action buttons that enable staff to update medicine information, record new transactions, or generate a detailed forecast report for this specific item. All information is presented in a responsive layout that adapts to different screen sizes, ensuring the page remains usable on various devices throughout the medical center. This detailed medicine view serves as a crucial tool for inventory managers, pharmacists, and administrators who need comprehensive information about specific medicines to make informed stocking decisions and ensure continuous availability of essential medications for patient care. 
 
 	 	 	 	Figure 10. Medicine Forecast Visualization 
The Medicine Forecast Visualization represents one of the most sophisticated aspects of the BMC Medicine Forecasting System. This specialized page provides an in-depth analysis of forecasting data for individual medicines, offering healthcare staff unprecedented insight into future demand patterns. 
When staff access this page for a specific medicine, they're greeted with a comprehensive forecasting dashboard that breaks down predicted demand in remarkable detail. The page begins with a summary section showing the medicine name, current stock level, and a quick-reference forecast highlighting expected demand for the next 30, 60, and 90 days. this page particularly valuable is the advanced visualization section. A large, interactive time-series chart dominates the center of the page, displaying historical consumption data alongside future predictions. The system plots actual past usage with solid lines and forecasted demand with dashed lines, creating a clear visual distinction between historical data and predictions. Confidence intervals appear as shaded regions around the forecast line, visually representing the range of potential demand scenarios and their probability. The forecast methodology section explains which algorithm was automatically selected for this specific medicine. The system evaluates multiple forecasting methods including 
Gradient Boosting, ARIMA, Holtwinters, Moving Average, Linear Regression, Polynomial Regression, and Ensemble approaches, then selects the one that delivers the highest accuracy for this particular medicine's demand pattern. Staff can see exactly why a specific algorithm was chosen, with comparative accuracy metrics for each method tested. Below the visualization, a detailed metrics section provides numerical analysis of the forecast quality, including accuracy percentage, Mean Absolute Percentage Error (MAPE), Mean Absolute Error (MAE), and Root Mean Square Error (RMSE). These metrics are presented alongside their historical trends, showing how forecast quality has improved over time as the system learns from new data. 
The page also included a seasonality analysis section that identified recurring patterns in demand, highlighting weekly, monthly, or seasonal trends specific to this medicine. This helped staff understand not just how much stock was needed, but when demand was likely to spike or decline. 
A forecast breakdown table presents the numerical predictions in tabular format, showing expected demand quantities for each future time period alongside confidence intervals and potential stock implications. The system automatically flags periods where predicted demand might exceed available stock based on current inventory levels and reorder timelines. 
This detailed forecast view represents the culmination of the system's advanced predictive capabilities, transforming complex statistical analysis into actionable insights that support proactive inventory management and ensure medicine availability for patient care. 
 
 
 
 
Figure 12. Medicine Demand Pattern part 1 
 
Figure 12. Medicine Seasonal Pattern part 2 
 
The Medicine Seasonal Pattern delivers a sophisticated analytical perspective on medicine inventory data that goes beyond basic reporting. This specialized page combines statistical analysis with visual representations to provide healthcare staff with deeper insights into medicine consumption patterns and inventory performance. 
When accessing the medicine analysis page, staff are presented with a comprehensive dashboard that breaks down medicine usage from multiple angles. The page opens with a high-level summary showing key performance indicators such as total inventory value, average turnover rate, and overall stock efficiency metrics. 
What sets this page apart is its multi-dimensional analysis approach. The consumption patterns section features interactive time-series charts that reveal how medicine usage has evolved over different time periods. These visualizations automatically highlight seasonal trends, unexpected spikes, and long-term patterns that might not be obvious from raw transaction data. Staff can toggle between daily, weekly, monthly, and quarterly views to identify patterns at different time scales. 
The category analysis section provides a comparative breakdown of medicine usage by therapeutic category, showing which medicine types are experiencing growing demand versus declining usage. This helps identify shifting treatment patterns and emerging healthcare needs within the facility. A heat map visualization effectively shows the relative demand intensity across different medicine categories and time periods. 
For inventory optimization, the page includes a sophisticated ABC analysis section that automatically classifies medicines into high-value/high-usage (A), moderatevalue/moderate-usage (B), and low-value/low-usage (C) categories. This classification helps prioritize inventory management efforts toward the medicines that have the greatest financial and clinical impact. 
The stockout risk analysis section uses historical stock levels and consumption patterns to identify medicines with the highest risk of future stockouts. The system calculates a risk score for each medicine based on factors like demand variability, lead time uncertainty, and historical stock performance. Medicines with elevated risk scores are prominently highlighted with recommendations for adjusted reorder points or safety stock levels. 
A correlation matrix visualization shows relationships between different medicines, revealing which items tend to be prescribed or consumed together. This helps staff understand potential complementary or substitution relationships between different products. 
The page also includes a financial performance section that analyzes inventory carrying costs, turnover rates, and value trends over time. This helps identify opportunities to reduce excess inventory while maintaining service levels. 
For medicines with expiration dates, an expiry risk analysis shows potential wastage risks and recommends optimal order quantities to minimize expired product losses. 
All analyses on the page can be filtered by date range, medicine category, or specific medicines of interest. The interactive nature of the visualizations allows staff to drill down into specific data points for more detailed information. 
This analytical view transforms raw inventory data into actionable insights, supporting evidence-based decisions about procurement strategies, stocking levels, and inventory policies. The page represents a significant advancement beyond traditional inventory reports, leveraging statistical methods to extract meaningful patterns from complex pharmaceutical usage data. 
 
Figure 14. Reports 
The figure displays the Reports Dashboard of the BMC Medicine Forecasting System, which serves as a centralized hub for generating comprehensive inventory analytics and forecasting reports. This interface exemplifies how the system transforms complex forecasting data into actionable business intelligence for healthcare administrators. 
The lower section of the dashboard offers four specialized report generation modules, each designed to address specific inventory management needs: 
Sales Report: Allows administrators to analyze financial performance over customizable time periods, with the current selection set to "Last 30 days." This report would incorporate data from the forecasting algorithms to contextualize sales against predicted demand. 
Forecast Report: Provides forward-looking inventory projections for the "Next 30 days," leveraging the system's seven forecasting algorithms to predict future medication requirements. This report directly translates the outputs of the forecasting simulation into actionable procurement recommendations. 
Inventory Report: Focuses on current stock status with particular emphasis on critical categories including "Low Stock Items" and "Expired Items." This report integrates forecasting data to prioritize replenishment actions based on predicted future demand. 
Transactions Report: Offers detailed analysis of medication dispensing activities over the "Last 30 days," providing insights into consumption patterns that inform the forecasting algorithms. 
Each report module offers flexible output options in both Excel and PDF formats, allowing for both detailed data analysis and formal documentation. The consistent design of the report generation interface demonstrates the system's emphasis on usability, enabling nontechnical healthcare administrators to access sophisticated forecasting insights without requiring specialized analytical skills. 
 
 
 
Figure 15. Dashboard Part 1 
 
 

Figure 15. Dashboard Part 2 
The System's Dashboard page is the heart of the inventory management system, giving you a complete picture of your medicine supply status at a glance. When you open the dashboard, you'll immediately notice how it combines critical information with powerful forecasting tools in one clean interface. The summary cards at the top show you exactly what you need to know right away - how many medicines you have, which ones are running low, your recent sales performance, and how far ahead the system is predicting demand. The forecasting section is where the system really shines, with our Gradient Boosting algorithm taking center stage. This advanced machine learning approach has transformed how we predict medicine needs, analyzing complex patterns in your historical data to make remarkably accurate forecasts. You can see this in the accuracy metrics displayed right on the dashboard, with most medicines now benefiting from this superior forecasting method. The visual charts make it easy to understand your inventory distribution and future sales trends without having to dig through reports. At the bottom, you'll find practical operational data showing your most recent transactions and highlighting medicines that need your immediate attention. Behind all this is sophisticated technology that pulls real-time data, calculates complex metrics, and presents everything in an intuitive way that works on any device. The system handles all the heavy lifting automatically, updating forecasts daily at 7 PM so you always have the latest predictions to guide your inventory decisions. 
 
 
 
 
Figure 16. About 
The about  serves as the informational cornerstone of the BMC Medicine Forecasting 
System, providing essential context about the system's purpose, capabilities. This page combines professional presentation with accessible language to help users understand the system's value to Bulacan Medical Center's operations. 
Upon visiting the About Us page, staff are greeted with a clean, well-structured layout that begins with an overview of the system's mission. The page explains how the forecasting system was specifically developed to address the unique inventory management challenges faced by healthcare facilities, particularly the critical nature of medicine availability and the consequences of stockouts or overstocking. 
The page is thoughtfully divided into several key sections that build a comprehensive understanding of the system.  
What makes this page particularly valuable is the "Technology" section, which explains the advanced forecasting methodologies in accessible terms. This section provides clear explanations of the various algorithms used, with special emphasis on the Gradient Boosting approach. Rather than overwhelming readers with technical jargon, the page uses straightforward language and visual aids to explain how these algorithms analyze historical data to predict future demand. The benefits of Gradient Boosting are highlighted, explaining how this advanced machine learning technique identifies complex patterns in medicine consumption that simpler methods might miss. 
The "Benefits" section outlines the tangible improvements the system brings to inventory management, including reduced stockouts, lower carrying costs, decreased medicine expiration, and improved patient care through consistent medicine availability. This section includes actual performance metrics showing how the system has improved inventory efficiency since implementation. 
For staff interested in the technical aspects, the "How It Works" section provides a more detailed explanation of the system's architecture, data flow, and decision-making process. This helps build trust by showing the expertise behind the platform. 
A "Future Developments" section outlines planned enhancements and additional features, giving users insight into the system's roadmap and demonstrating ongoing commitment to improvement. 
The page concludes with contact information for support and feedback, encouraging user engagement with the system. Throughout the page, the tone remains professional yet accessible, avoiding overly technical language while still conveying the sophistication of the solution. 
 
 
Figure 17. Audit trail 
The template represents a critical accountability and transparency feature within the BMC 
Medicine Forecasting System. This specialized page provides a comprehensive chronological record of all system activities, supporting regulatory compliance and enabling detailed operational oversight. 
When staff access the audit trail page, they encounter a meticulously organized log of system events presented in a clear, tabular format. The page opens with a concise summary showing the total number of recorded actions and the date range covered by the audit records. A prominent timestamp indicates when the audit data was last refreshed, ensuring staff know they're viewing current information. 
What distinguishes this page is its comprehensive tracking approach. The audit trail captures a wide range of system events including inventory adjustments, medicine additions or removals, transaction recordings, forecast generations, user logins, permission changes, and configuration modifications. Each entry in the audit log contains essential details: the specific action performed, the user who performed it, the exact timestamp, the affected item (such as a specific medicine or transaction), and the before/after values where applicable. 
The page features robust filtering capabilities that allow staff to narrow down the extensive audit data based on multiple criteria. Users can filter by date range, action type, user, affected item, or search for specific text across all fields. This filtering system helps quickly locate relevant audit information when investigating specific events or preparing compliance reports. 
For regulatory and accountability purposes, the audit trail cannot be modified or deleted by regular users, ensuring the integrity of the historical record. The system automatically maintains audit records according to the facility's data retention policies, with older records archived rather than deleted to maintain a complete historical record when needed. 
The page includes a detailed view option that allows staff to expand any audit entry to see complete before/after information, revealing exactly what changed in each recorded action. For complex changes, the system highlights specific fields that were modified, making it easy to identify the precise nature of each change. 
Security-related events receive special highlighting, ensuring that permission changes, unusual login patterns, or sensitive data access stands out visually in the audit record. The system also flags potentially problematic patterns, such as unusual quantities of inventory adjustments or transactions outside normal operating hours. 
For facilities with strict regulatory requirements, the audit system supports digital signatures and tamper-evident logging to provide additional assurance of data integrity. The page also includes links to related regulatory guidance and internal policies regarding data tracking requirements. 
 
Figure 18. Medicine Transaction List 
 
The template serves as the operational backbone of the BMC Medicine Forecasting System, providing a comprehensive view of all medicine movements throughout the facility. This meticulously designed page transforms raw transaction data into an organized, actionable record that supports both daily operations and long-term analysis. 
When staff navigate to the transaction list, they're presented with a clean, tabular interface that chronologically displays medicine transactions with remarkable clarity. The page opens with a summary dashboard showing key metrics: total transaction count, transaction value for the selected period, and a quick breakdown of sales versus purchases. This immediate overview helps establish context before diving into individual records. 
What makes this page particularly effective is its thoughtful organization of complex transaction data. Each transaction entry displays essential information in a logical sequence: date and time, transaction type (sale or purchase), medicine name, quantity, unit price, total amount, and the staff member who recorded it. For patient-related transactions, the system also displays the customer or patient identifier while maintaining appropriate privacy controls. 
The filtering system represents one of the page's most practical features. Staff can quickly narrow down thousands of transactions using multiple criteria simultaneously: date ranges (today, this week, this month, custom range), transaction types, specific medicines, value ranges, or by searching across any field. This filtering capability transforms what could be an overwhelming amount of data into precisely the information needed for specific operational questions. 
The page incorporates visual cues that enhance information clarity. Different transaction types are color-coded (typically green for purchases, blue for sales), while transactions with unusual quantities or values are subtly highlighted to draw attention. Expired medicine transactions receive special visual indicators, as do transactions related to controlled substances or high-value items. 
For each transaction, the system provides context-sensitive action buttons that adapt based on the transaction type and age. Recent transactions might show options for editing or voiding (with appropriate authorization), while all transactions offer options to view details, print receipts, or view related audit trail entries. 
The page includes a responsive data table that adapts to different screen sizes without sacrificing information density. On mobile devices, less critical columns automatically collapse into expandable details, ensuring the most important information remains visible while still allowing access to complete records when needed. 
For analytical purposes, the transaction list includes quick-access visualization tools that can generate trend charts directly from the filtered data. Staff can quickly visualize transaction patterns over time, helping identify unusual activity or emerging trends without leaving the transaction view. 
The page supports comprehensive export functionality, allowing authorized staff to download transaction data in Excel or PDF format with professional business-style formatting. These exports include proper headers, summaries, and formatting suitable for financial reconciliation, inventory audits, or management reports. 
Pagination controls and adjustable page size options help manage large transaction volumes efficiently, while a "sticky" header ensures column titles remain visible when scrolling through extensive transaction lists. 
This transaction list implementation balances comprehensive data access with usability considerations, providing the detailed transaction visibility needed for healthcare inventory management while presenting the information in an accessible, actionable format. The system serves as both an operational tool for daily medicine management and a historical record for analysis and accountability. 
 
 
Figure 19. Medicine List 
 
The template serves as the central inventory management hub within the BMC Medicine 
Forecasting System, providing a comprehensive view of all medicines currently stocked at the facility. This meticulously crafted page transforms complex inventory data into an accessible, actionable format that supports efficient medicine management. 
When staff navigate to the inventory list, they encounter a well-organized tabular interface that presents the complete medicine inventory with remarkable clarity. The page opens with a summary dashboard showing key metrics: total inventory count, total inventory value, number of low-stock items, and number of items approaching expiration. This immediate overview establishes context before staff dive into individual inventory records. What makes this page particularly effective is its thoughtful organization of essential inventory information. Each medicine entry displays critical data in a logical sequence: name, current quantity, reorder level, category, unit price, total value, and last transaction date. The system uses visual indicators to highlight inventory status – items below reorder level appear with warning indicators, while out-of-stock items receive more prominent alerts. Medicines approaching expiration dates are similarly highlighted with color-coded indicators based on urgency. 
The filtering system represents one of the page's most practical features. Staff can quickly narrow down hundreds or thousands of inventory items using multiple criteria simultaneously: medicine name, category, stock status (all, low stock, out of stock), expiration status, or value range. This filtering capability transforms what could be an overwhelming inventory list into precisely the information needed for specific operational questions. 
The page incorporates a responsive data table that adapts to different screen sizes without sacrificing information density. On mobile devices, less critical columns automatically collapse into expandable details, ensuring the most important information remains visible while still allowing access to complete records when needed. 
For each medicine, the system provides context-sensitive action buttons that adapt based on the item's status. Low-stock items show prominent "Reorder" buttons that pre-populate purchase transaction forms, while all items offer options to view details, transaction history, or forecast information. This action-oriented approach helps staff quickly respond to inventory situations that require attention. 
The inventory list includes batch tracking information for medicines where applicable, showing quantity breakdowns by expiration date. This supports proper rotation of stock and helps prevent expiration waste through visual indicators of approaching expiration dates. 
The page supports comprehensive export functionality, allowing authorized staff to download inventory data in Excel or PDF format with professional business-style formatting. These exports include proper headers, summaries, and formatting suitable for inventory audits, valuation reports, or management reviews. 
A unique feature of the inventory list is its integration with the forecasting system. Each medicine entry includes a small sparkline chart showing recent consumption trends and forecast direction, providing immediate visual cues about expected future demand without requiring navigation to detailed forecast pages. Items with unusual forecast patterns receive special indicators, helping staff identify potential supply challenges before they become 
critical. 
The page also includes a batch operations section that allows authorized staff to perform actions on multiple inventory items simultaneously, such as category reassignment, batch price updates, or generating reorder lists for multiple low-stock items. 
This inventory list implementation balances comprehensive data access with usability considerations, providing the detailed inventory visibility needed for healthcare inventory management while presenting the information in an accessible, actionable format. The system serves as both an operational tool for daily medicine management and a strategic resource for inventory optimization and planning. 
 
 
Figure 21. Add Medicines 
 
The template represents a critical data entry interface within the BMC Medicine 
Forecasting System, enabling staff to add new medicines to the inventory database with precision and completeness. This thoughtfully designed page transforms what could be a complex data entry task into a structured, intuitive process that ensures accurate medicine records from the outset. 
When staff access the medicine creation page, they encounter a clean, well-organized form divided into logical sections that guide them through the complete medicine registration process. The page opens with a clear title and brief instructions, immediately establishing the purpose and workflow of the form. 
What distinguishes this page is its comprehensive approach to medicine data collection. The form begins with essential identification fields including medicine name, generic name, brand name, and manufacturer. The name field includes real-time validation that checks for potential duplicates in the existing inventory, helping prevent accidental creation of duplicate records while still allowing intentional additions of similar medicines. The classification section allows staff to categorize the medicine according to multiple taxonomies simultaneously: therapeutic category, dosage form, administration route, and custom classification tags. These categories support both operational organization and analytical reporting. The system provides standardized options through dropdowns while allowing new categories to be added when needed, balancing standardization with 
flexibility. 
For inventory management, the form includes critical fields for initial quantity, unit of measure, reorder level, and reorder quantity. Helpful tooltips explain how these values affect inventory alerts and automatic reordering suggestions. The system provides guidance on appropriate reorder levels based on typical usage patterns for similar medicines, helping establish sensible initial values. 
The pricing section captures both purchase cost and selling price, automatically calculating and displaying the margin percentage. For medicines with variable pricing, the form allows multiple price tiers to be defined. The system also includes fields for tax information and special pricing notes. 
For medicines with expiration concerns, the form includes batch tracking fields where staff can record batch numbers, manufacturing dates, and expiration dates. The system supports recording multiple batches with different expiration dates during initial creation, eliminating the need for separate batch entries. 
The regulatory section captures important compliance information including controlled substance classification, prescription requirements, and any special handling instructions. For controlled substances, additional fields appear to record the specific schedules and regulatory requirements. 
The form includes a clinical information section where staff can record dosage instructions, contraindications, side effects, and interactions with other medicines. While not replacing formal clinical systems, this information helps inventory staff understand special handling or storage requirements. 
The page incorporates intelligent validation that checks for common data entry errors: unusually high or low prices compared to similar medicines, unrealistic expiration dates, or missing critical information. The validation provides specific, helpful error messages rather than generic warnings. 
Before final submission, the page displays a summary of the entered information for verification. Upon successful creation, the system confirms the addition and offers options to either add another medicine or view the newly created medicine detail page. 
The entire interface adapts responsively to different screen sizes, with special consideration for tablet layouts that might be used during inventory receiving processes. Field sizes, button placements, and form organization are optimized for both accuracy and efficiency. 
 
Figure 21. Medicine Details 
The template serves as a comprehensive information hub for individual medicines within the BMC Medicine Forecasting System. This page presents detailed information about a specific medicine in a structured, easy-to-navigate format that supports informed inventory management decisions. 
Upon accessing a medicine's detail page, healthcare staff are presented with a wellorganized layout that includes critical information about the selected medicine. The page header displays the medicine name prominently, accompanied by its category classification and current stock status. Below this, the page is divided into several key information sections that provide a complete picture of the medicine's profile. 
The inventory section shows current quantity, reorder level, and reorder quantity, with visual indicators that clearly highlight when stock levels are approaching or below the reorder threshold. This section also includes expiration date information with automatic warnings when medicines are nearing expiration, helping prevent waste and ensure patient safety. 
The financial section displays pricing information, including purchase price, selling price, and profit margin calculations. Historical transaction data is presented in a chronological table, allowing staff to review past purchases and sales of this specific medicine. This transaction history provides valuable context for understanding usage patterns. 
What distinguishes this page is the dedicated forecasting section, which shows demand predictions specifically for this medicine.  
 
3.0 Decision Support and Forecasting  
The decision support mechanism is integrated into the BMC Medicine Forecasting System through the forecasting algorithm, wherein it determines the supply and demand of the medical inventory in the mill. 
The decision support system refers to a computerized program used to support determinations, judgments, and courses of action in an organizational or clinical context. 
It helps decision-makers analyze data and make informed choices. 
The forecasting algorithm provides predictive insights by analyzing historical inventory and consumption data to estimate future demand. This enables proactive planning and efficient resource allocation. 
To simulate the forecasting algorithm, the researcher used multiple forecasting methods allowing for cross-validation and improved accuracy of predictions. 
The BMC Medicine Forecasting System incorporates sophisticated decision support and forecasting capabilities that transform raw inventory data into actionable insights for medical resource management. This system represents a significant advancement over traditional inventory approaches by leveraging multiple predictive algorithms to optimize medicine supply chains. 
Forecasting Methodology 
The forecasting engine within the BMC Medicine Forecasting System evaluates seven distinct algorithms for each medicine in the inventory to ensure accurate and adaptable predictions. These algorithms include Holt-Winters, which uses triple exponential smoothing to account for level, trend, and seasonality in demand patterns; Moving 
Average, which offers baseline forecasts based on recent consumption trends; and Linear Regression, which detects straightforward linear trends in usage data. To address more complex, non-linear demand patterns, the engine employs Polynomial Regression. ARIMA (AutoRegressive Integrated Moving Average) is used to model time-series data with potential seasonal fluctuations, providing robust forecasts in dynamic environments. 
Additionally, Ensemble Methods combine multiple algorithms to enhance predictive accuracy by leveraging the strengths of each model. The most advanced among them, Gradient Boosting, uncovers intricate relationships in the data, making it highly effective for identifying nuanced trends in medicine consumption. Together, these algorithms form a comprehensive and adaptive forecasting suite tailored to optimize medical supply chain management. 
To simulate the forecasting algorithms, the following figures present the actual forecasted values generated by these methods.  
 
 	 	 	Figure 22. Medicine Forecast Simulation 
The figure presents a detailed forecast analysis for Metoprolol, a beta-blocker commonly used to treat high blood pressure and heart-related conditions. This visualization exemplifies how the BMC Medicine Forecasting System translates complex algorithmic outputs into actionable insights for healthcare inventory management. 
The interface displays critical inventory metrics for Metoprolol over a six-month forecast period (October 2024 to April 2025). The system has determined an actual demand of 109 pieces against a current stock of 40 pieces, with a reorder level set at 24 pieces. Based on comprehensive analysis of historical consumption patterns, the system recommends an order of 7 pieces to maintain optimal inventory levels. 
The quarterly analysis section provides deeper insights into seasonal demand variations. The bar chart illustrates average quarterly demand patterns, with Q3 and Q4 showing notably higher consumption rates compared to Q1 and Q2. This seasonal fluctuation is tracked alongside standard deviation (represented by the red line), which indicates the reliability of the forecast predictions. The higher standard deviation in Q3 and Q4 suggests greater variability in demand during these periods, which inventory managers should account for in their planning. 
The medicine information panel provides contextual data that influences the forecast calculations. Metoprolol is supplied by AbbVie under the brand name Lopressor, with the generic medicine classification also listed. This information is crucial as the forecasting algorithms incorporate brand-specific and supplier-specific variables when generating predictions. 
What makes this interface particularly valuable is its translation of complex statistical forecasting into clear, actionable recommendations. Rather than overwhelming users with technical details of the underlying algorithms, the system distills the outputs into a straightforward order recommendation of 7 pieces, with supporting data visualizations that explain the rationale behind this suggestion. 
This approach represents a significant advancement over traditional inventory management systems that rely solely on reorder points or simple moving averages. By integrating multiple forecasting methodologies and presenting their outputs in an accessible format, the BMC Medicine Forecasting System empowers healthcare administrators to make datadriven decisions that optimize resource allocation while ensuring medicine availability for patient care. 
 
 
Figure 23. Demand Simulation 
 	The figure provides a granular view of Metoprolol consumption patterns through two complementary data visualizations: monthly demand trends and recent transaction history. This detailed temporal analysis reveals critical insights that inform the forecasting algorithms within the BMC Medicine Forecasting System. 
The monthly demand table presents a striking consumption pattern for Metoprolol over a six-month period. The data shows minimal activity during the initial three months (November 2024 through January 2025), followed by a dramatic increase in consumption beginning in February 2025 (16 pieces), escalating significantly in March (59 pieces), and peaking in April (101 pieces). This exponential growth pattern is particularly noteworthy as it demonstrates a clear seasonal trend that would be captured by the system's Holt-Winters algorithm, which specializes in detecting seasonality in time-series data. 
The recent transactions panel provides micro-level insights into current consumption behaviors. All five displayed transactions occurred on April 24, 2025, each involving the sale of a single piece of Metoprolol. This granular transaction data serves multiple purposes within the forecasting system. First, it provides real-time validation of the monthly aggregated data. Second, it offers insights into dispensing patterns—in this case, suggesting that Metoprolol is being prescribed to individual patients in single-unit quantities rather than in bulk distributions. 
The juxtaposition of these two data visualizations demonstrates how the BMC Medicine Forecasting System integrates multiple temporal scales in its analysis. The monthly view captures macro-level trends essential for long-term planning, while the transaction history provides micro-level insights that help validate forecasting assumptions and detect emerging patterns that might not yet be visible in aggregated data. 
From a methodological perspective, this multi-scale temporal analysis is crucial for accurate forecasting. The system's ARIMA algorithm would leverage the time-series properties evident in the monthly data, while the Gradient Boosting algorithm would incorporate the granular transaction patterns to refine predictions. The clear seasonal trend visible in the monthly data would likely lead the system to prioritize forecasting methods that excel at capturing seasonality, such as Holt-Winters or Polynomial Regression. 
 
 
 
Figure 24. Methods simulation 
In the figure employs an intelligent algorithm-selection framework that automatically matches each medication with its optimal forecasting method from seven available algorithms. This adaptive approach is evident in the displayed interface, where different medications utilize different forecasting techniques: Metoprolol uses Polynomial regression, Albuterol and Omeprazole employ Gradient Boosting, Metformin leverages Moving Average, and Losartan utilizes an Ensemble method. The system determines these pairings by analyzing each medication's historical consumption patterns and selecting the algorithm that produces the highest prediction accuracy. For each medication, the system calculates expected consumption with confidence intervals (shown in brackets), quantifies forecast reliability (displayed as accuracy percentages), and compares current stock against minimum thresholds to generate actionable inventory recommendations. This medicationspecific approach enables the system to handle diverse pharmaceutical consumption patterns within a unified framework, automatically flagging critical inventory situations (like Albuterol's zero stock) while providing precise reordering recommendations calibrated to each medication's unique demand characteristics. 
 
 
Figure 26. Polynomial 
 	The BMC Medicine Forecasting System has selected Polynomial Regression as the optimal forecasting method for Metoprolol, as shown in the detailed forecast view. This algorithmic choice reveals important insights about this medication's consumption patterns. Polynomial Regression is particularly effective for medications that exhibit nonlinear trends with moderate complexity, suggesting Metoprolol's usage follows a curved pattern that cannot be adequately captured by simpler linear models. 
The system has made this determination based on 65 data points collected over 60 days, with a data quality rating of 17.8% - indicating sufficient but not optimal historical data. Notably, the analysis shows no significant seasonality or trend strength for this medication, which further justifies the polynomial approach as it can model subtle nonlinear patterns without overreacting to noise or requiring strong cyclical components. 
The 30-day forecast period using this method has yielded high accuracy (85% as shown in the previous view), demonstrating the algorithm's effectiveness for this specific medication. With current stock (40 units) well above the reorder level (24 units) and the polynomial model predicting moderate consumption, the system correctly recommends no immediate action. This exemplifies how the forecasting system's algorithm-selection logic adapts to each medication's unique consumption characteristics, applying more sophisticated mathematical models only where they provide demonstrable forecasting advantages 
 
 
Figure 26. Gradient Boosting 
The Forecasting System has selected Gradient Boosting as the optimal forecasting method for Albuterol, revealing significant insights about this medication's consumption patterns. Gradient Boosting represents the system's most sophisticated machine learning approach, typically deployed for medications with complex, multi-variable dependencies that simpler statistical methods cannot adequately capture. 
This algorithmic selection is particularly noteworthy given Albuterol's critical inventory situation (0 current stock against a reorder level of 39 units). The system has based this forecast on 50 data points collected over 60 days, with a data quality rating of 13.7% - indicating somewhat limited but still actionable historical data. Similar to Metoprolol, the analysis shows no significant seasonality or trend strength for Albuterol, suggesting its consumption pattern is driven by more complex factors than simple time-based trends. 
What distinguishes Gradient Boosting is its ability to iteratively learn from previous prediction errors, building an ensemble of decision trees that collectively produce highly accurate forecasts even with irregular or seemingly unpredictable consumption patterns. This makes it particularly valuable for medications like Albuterol that may be prescribed episodically or in response to specific patient conditions rather than following predictable usage curves. 
 
 
Figure 28. Forecast Visualization 
The multi-horizon forecasting display for this medication perfectly exemplifies the BMC Medicine Forecasting System's sophisticated implementation of its seven-algorithm approach described in the documentation. The consistent weekly demand pattern of 13 units with occasional minor fluctuations (12-14 units) and tight ±10 confidence intervals demonstrates the system's application of Moving Average and Holt-Winters methods for short-term stability, while the gradually increasing monthly forecasts (32→37 units) reveal the influence of Polynomial Regression capturing subtle upward trends that simple Linear Regression might miss. This aligns with the earlier screens showing Polynomial Regression selected for Metoprolol and Gradient Boosting for Albuterol, confirming how the system dynamically selects optimal algorithms for each medication. The remarkably stable yearly projections (100-101 units) with ±20 confidence bands indicate ARIMA and Ensemble Methods working together to produce robust long-term predictions by balancing multiple algorithmic inputs. Most importantly, these forecasts directly support the system's core decision support function by providing actionable insights across multiple time horizons - enabling immediate inventory decisions (weekly), procurement planning (monthly), and strategic resource allocation (yearly) - all derived from the same integrated dataset of 50-65 historical data points mentioned in the previous medication detail screens. This comprehensive approach transforms raw inventory data into the precise, multidimensional forecasts shown here, validating the system's capability to optimize medical supply chains through advanced predictive analytics. 
4.0 System Evaluation Using ISO/IEC 25010 Software Quality Model  
To assess the performance of the system, the researcher used ISO / IEC 25010 with the following criteria such as functionality, performance, compatibility, usability, reliability, security and maintainability.  
System Performance based from the Characteristics of ISO/IEC 25010 or the Software  
An evaluation instrument was developed based on the ISO/IEC 25010 Software Product 
Quality Model criteria. Respondents assessed the system using a Likert scale model designed for this study. The collected data were processed using the Statistical Package for the Social Sciences (SPSS) and analyzed through appropriate statistical methods. The arithmetic mean was applied to determine the overall performance of each indicator. To evaluate the system’s quality, the study utilized the ISO/IEC 25010 criteria which include functionality, performance efficiency, compatibility, usability, reliability, security, maintainability and safety. ISO/IEC 25010 is an international standard that defines a model for evaluating the quality of software products. ISO/IEC 25010 helps developers, testers, and stakeholders ensure that software is high-quality, user-friendly, and fit for its intended purpose 
Table 3 
Rating Scale and Evaluation Interpretation 
Table 3 provides the rating scale for the lower bound and upper bound together with the interpretation. 
 
 
 
As gleaned in Table 4, in terms of functional suitability the respondents recorded a mean performance of 4.44 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's ability to cover all the specified tasks and user objectives with a mean of 4.52. Further, they added that this characteristic can be evaluated through the functions and services provided to the user, which received positive ratings for accuracy and appropriateness. 
 
As gleaned in Table 5, in terms of reliability the respondents recorded a mean performance of 4.19 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's recoverability with a mean of 4.29, indicating that in the event of an interruption or failure, the system can effectively recover data and reestablish the desired state. Further, they added that the system demonstrates good operational accessibility and fault tolerance capabilities that support consistent performance under various conditions. 
Table 6 
Portability 
 
As gleaned in Table 6, in terms of portability the respondents recorded a mean performance of 4.32 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's adaptability to different environments and its ability to increase efficiency and productivity. Further, they added that the system demonstrates good installability characteristics and can effectively replace other software products for the same purpose, supporting smooth transitions between different operational environments. 
 
As gleaned in Table 7, in terms of usability the respondents recorded a mean performance of 4.42 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's learnability and user error protection features, indicating that users can effectively learn and safely operate the system. Further, they added that the system demonstrates good appropriateness recognizability, allowing users to easily determine whether the system meets their needs, and provides an interface that enables pleasing and satisfying interactions. 
 
As gleaned in Table 8, in terms of performance efficiency the respondents recorded a mean performance of 4.45 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's time behavior, indicating that response times and throughput rates meet requirements when performing functions. Further, they added that the system demonstrates efficient resource utilization and appropriate capacity limits, which are particularly important for supporting the Gradient Boosting algorithm implementation that requires more computational resources than simpler forecasting methods but delivers superior accuracy. 
 	 
 
As gleaned in Table 9, in terms of security the respondents recorded a mean performance of 4.31 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's confidentiality features, ensuring that data are accessible only to those authorized to have access. Further, they added that the system demonstrates effective integrity protection, preventing unauthorized access to or modification of data, which is particularly important given the sensitive nature of pharmaceutical inventory management in a healthcare setting.  
 
As gleaned in Table 10, in terms of compatibility the respondents recorded a mean performance of 4.30 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's co-existence capabilities, allowing it to perform required functions efficiently while sharing resources with other products. Further, they added that the system demonstrates effective interoperability, enabling it to exchange and use information with other systems, which is essential for ensuring that the forecasting system can access transaction data from point-of-sale systems and share inventory recommendations with procurement systems. 
 
 
As gleaned in Table 11, in terms of maintainability the respondents recorded a mean performance of 4.33 which has an interpretation result of "Acceptable". The highest means are recorded in terms of the system's testability features, allowing test criteria to be established and tests performed to determine whether those criteria have been met. Further, they added that the system demonstrates good analyzability and modularity, enabling effective assessment of the impact of changes and ensuring that modifications to one component have minimal impact on others, which supports efficient maintenance and future enhancements. 
 
 
 
As gleaned in Table 12, the comprehensive evaluation of the BMC Medicine Forecasting System across all eight quality dimensions yielded a grand mean of 4.35 which has an interpretation result of "Very Acceptable". The highest means are recorded in terms of Performance Efficiency (4.45) and Functional Suitability (4.44), followed by Usability (4.42) and Maintainability (4.33). Further, they added that these results demonstrate the system performs well across all quality dimensions, with particular strengths in its performance efficiency and functional capabilities that effectively support medicine inventory management and forecasting 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Chapter 4 
Summary of Findings, Conclusion, and Recommendations 
 
This chapter presents a summary of the key findings from the study, draws conclusions based on the analysis of the implementation results and survey data, and provides recommendations for future enhancements and applications of the inventory optimization system at the Bulacan Medical Center. 
Summary of Findings 
Problem 1: What are the existing methods employed by Bulacan Medical 
Center for managing medicine supplies? 
In answer to the first research question on the existing methods employed by Bulacan Medical Center for managing medicine supplies, the study found that the center primarily relied on manual and semi-automated processes for inventory management. The existing system involved outdated system record-keeping supplemented by basic spreadsheet applications for tracking inventory levels. Medicine stock counts were conducted manually on a periodic basis, typically monthly, which led to delays in identifying stockouts or overstocking situations. Order quantities were determined based on historical usage patterns estimated by pharmacy staff, without the benefit of advanced analytical tools. This approach resulted in frequent inventory discrepancies, inefficient resource allocation, and challenges in maintaining optimal stock levels. 
The study also revealed that the Bulacan Medical Center's previous inventory management system lacked real-time visibility into stock levels, making it difficult to respond promptly to changing demand patterns or supply chain disruptions. The manual nature of the existing system was time-consuming and prone to human error, with staff spending significant time on administrative tasks rather than patient care activities. Additionally, the lack of automated forecasting capabilities meant that inventory decisions were often reactive rather than proactive, leading to instances of medication stockouts or excess inventory that could result in expired medications. 
Problem 2: What are the salient features of the proposed system? 
The proposed BMC MedForecast system incorporates several key features designed to address the limitations of the existing inventory management approach. The system utilizes multiple forecasting algorithms to forecast medicine demand with high accuracy, automatically selecting the most appropriate method for each medicine based on its specific demand pattern. This adaptive approach ensures optimal forecast accuracy across diverse inventory items, with the system capable of analyzing historical data, seasonal variations, and other factors influencing medicine demand. 
Other salient features include real-time inventory tracking and monitoring, which provides immediate visibility into stock levels and expiration dates. The system generates automated alerts for low stock levels or impending expiration dates, enabling proactive inventory management. Comprehensive reporting capabilities offer insights into inventory performance metrics, such as turnover rates, stockout frequencies, and forecast accuracy. 
The user-friendly interface is designed for ease of use by pharmacy staff, with intuitive dashboards and visualization tools that facilitate data-driven decision-making.  
Problem 3: How can the Decision Support system be integrated using the forecasting technique in terms of medical inventory? 
The Decision Support system can be effectively integrated using forecasting techniques in several ways. The study found that by implementing multiple forecasting algorithms (ARIMA, Linear Regression, Polynomial Regression, Moving Average, HoltWinters, and advanced machine learning methods), the system can analyze historical transaction data to identify patterns and predict future demand with high accuracy. The automatic method selection process ensures that each medicine is forecast using the most appropriate algorithm for its specific demand pattern, resulting in an average accuracy of 87.3% across all methods. 
The integration of forecasting techniques enables the Decision Support system to provide actionable recommendations for inventory management. These include optimal reorder points and quantities based on predicted demand, lead times, and safety stock requirements. The system also identifies seasonal trends and cyclical patterns in medicine consumption, allowing for proactive inventory planning. By analyzing the correlation between various factors (such as disease outbreaks, weather patterns, or hospital admission rates) and medicine demand, the system provides insights into demand drivers. Additionally, the forecasting capabilities support "what-if" scenario analysis, enabling pharmacy managers to evaluate the potential impact of different inventory strategies or external factors on medication availability and costs. 
Problem 4: How satisfied are users with the new digital inventory management system based on ISO/IEC 25010 standards? 
To assess user satisfaction with the new digital inventory management system, a comprehensive survey was conducted based on ISO/IEC 25010 standards. The survey included 20 respondents who used the inventory optimization system with the implemented forecasting functionality. The results for each quality dimension are as follows: 
Functional Completeness: Users rated the system's functional completeness at 4.43 on a 5-point scale, indicating a high level of satisfaction with the system's ability to provide all necessary functions for inventory management tasks. Respondents particularly appreciated the comprehensive coverage of inventory management processes, from demand forecasting to stock monitoring and reordering. 
Functional Correctness: The system received a rating of 4.43 for functional correctness, demonstrating users' confidence in the accuracy of the system's outputs and calculations. Respondents noted that the system's forecasting results and inventory recommendations were reliable and aligned with their professional judgment. 
Functional Appropriateness: With a rating of 4.38, users expressed strong satisfaction with how well the system's functions facilitated their inventory management tasks. Respondents highlighted that the system effectively streamlined workflows and reduced the complexity of inventory decision-making. 
Performance Efficiency: The system's performance efficiency received high ratings, with time behavior rated at 4.52, resource utilization at 4.43, and capacity at 4.38. 
These scores indicate that users were very satisfied with the system's responsiveness, resource usage, and ability to handle the required workload. 
Compatibility: Users rated co-existence at 4.29 and interoperability at 4.29, showing satisfaction with the system's ability to operate alongside other hospital systems without conflicts and to exchange information effectively with other applications. 
Usability: The system received excellent ratings for usability aspects, with operability at 4.62, learnability at 4.52, and user engagement at 4.38. These high scores reflect the system's intuitive interface and ease of use, which were critical for adoption by pharmacy staff. 
Reliability: Reliability metrics showed strong performance, with availability rated at 4.24, recoverability at 4.29, and fault tolerance at 4.05. While still positive, fault tolerance received the lowest rating among all metrics, suggesting an area for potential improvement. 
Security: Security aspects were well-received, with ratings consistently above 4.2, indicating users' confidence in the system's ability to protect sensitive inventory and medication data while maintaining appropriate access controls. 
Overall, the survey results demonstrate high user satisfaction across all ISO/IEC 25010 quality dimensions, with 87.25% of all ratings being either "Very Good" or "Excellent." This indicates that the new digital inventory management system effectively meets user needs and expectations at the Bulacan Medical Center. 
Conclusion 
Based on the findings of this study, the following conclusions can be drawn: 
Benefits of Multi-Method Forecasting Systems 
The study confirms that a multi-method forecasting system with automatic method selection provides optimal results for diverse inventory items at the Bulacan Medical Center. Different medicines exhibit various demand patterns, and no single forecasting method is optimal for all items. By implementing multiple algorithms and automatically selecting the most appropriate one for each medicine, the system achieves high forecast accuracy across the entire inventory, leading to improved inventory management and resource utilization. 
User Acceptance and System Quality 
The high user satisfaction ratings across multiple quality dimensions based on the ISO/IEC 25010 standards indicate that the system is well-designed, user-friendly, and effectively meets the needs of pharmacy and inventory management staff at the Bulacan Medical Center. The particularly strong ratings for operability, time behavior, and learnability suggest that the system can be easily adopted and effectively used in daily operations, which is crucial for realizing the benefits of advanced forecasting capabilities. 
Impact on Pharmaceutical Inventory Management 
The implementation of the advanced forecasting system has demonstrated significant potential for improving pharmaceutical inventory management at the Bulacan 
Medical Center. More accurate demand predictions enable better inventory planning, reducing both stockouts and excess inventory. This leads to improved patient care through medication availability and more efficient resource utilization, addressing key challenges in pharmaceutical supply chain management identified in Chapter 1. 
Recommendations 
Based on the findings and conclusions of this study, the following recommendations are proposed: 
System Enhancement Recommendations 
Improve Fault Tolerance and Error Handling: Enhance the system's 
resilience to errors and its ability to maintain functionality in the presence of faults, addressing the relatively lower ratings in these areas. This aligns with the continuous improvement approach outlined in the Agile methodology in Chapter 2. 
Expand Data Integration: Incorporate additional data sources, such as electronic health records, disease surveillance systems, and weather data, to further improve forecast accuracy by capturing external factors that may influence medicine demand at the Bulacan Medical Center. 
Implement Advanced Analytics: Develop more sophisticated analytical capabilities, such as anomaly detection and pattern recognition, to identify unusual demand patterns or potential inventory issues before they become critical. 
Enhance Visualization and Reporting: Expand the system's visualization and reporting capabilities to provide more intuitive and actionable insights from the forecasting results, supporting better decision-making by the pharmacy staff and inventory managers. 
Integrate with Procurement Systems: Develop integrations with procurement systems to automate the ordering process based on forecast results, further streamlining inventory management at the Bulacan Medical Center. 
Implementation Recommendations 
Phased Deployment: Implement the advanced forecasting system in phases, starting with high-value or critical medications, to manage change effectively and demonstrate value incrementally. This aligns with the Agile methodology outlined in Chapter 2. 
Comprehensive Training: Provide thorough training for all users at the Bulacan Medical Center, emphasizing the benefits of the advanced forecasting capabilities and how to interpret and act on the forecast results. 
Regular Performance Monitoring: Establish a process for regularly monitoring forecast accuracy and system performance, enabling continuous improvement and adaptation to changing demand patterns, in line with the Agile methodology described in Chapter 2. 
Feedback Mechanism: Implement a structured feedback mechanism to capture user experiences and suggestions, facilitating ongoing system refinement and enhancement, as outlined in the prototyping model in Chapter 2. 
Future Research Directions 
Comparative Analysis of Forecasting Methods: Conduct research comparing different forecasting approaches for pharmaceutical demand forecasting at the Bulacan Medical Center, identifying which methods work best for specific types of medications or demand patterns. 
Multi-echelon Inventory Optimization: Extend the research to multi-echelon inventory systems, exploring how advanced forecasting methods can optimize inventory across multiple levels of the pharmaceutical supply chain at the Bulacan Medical Center. 
Integration with Clinical Decision Support: Investigate the potential integration of demand forecasting with clinical decision support systems to align inventory management with clinical practice patterns at the Bulacan Medical Center. 
Explainable AI for Forecasting: Develop methods for making advanced forecasting models more interpretable and explainable to users, enhancing trust and adoption among the staff at the Bulacan Medical Center. 
Pandemic and Disruption Resilience: Research how advanced forecasting methods can be adapted to maintain accuracy during supply chain disruptions or demand pattern shifts, such as those experienced during pandemics, ensuring continuous medication availability at the Bulacan Medical Center. 
 
It is recommended that inferential statistical methods—such as t-tests or 
ANOVA—be employed in future assessments to determine whether observed differences in user satisfaction, forecast accuracy, and system performance across different user groups or settings are statistically significant. This will provide a more robust basis for generalizing the system’s effectiveness and guiding future improvements. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
References 
 
Abdali, N., Heidari, S., Alipour-Vaezi, M., Jolai, F., & Aghsami, A. (2025). A priority queueing-inventory approach for inventory management in multi-channel service retailing using machine learning algorithms. Kybernetes, 54(5), 2563-2591. 
Ahmadi, E., Mosadegh, H., Maihami, R., Ghalehkhondabi, I., Sun, M., & Süer, G. A. (2022). Intelligent inventory management approaches for perishable pharmaceutical products in a healthcare supply chain. Computers & Operations Research, 147, 105968. 
Amosu, O. R., Kumar, P., Ogunsuji, Y. M., Oni, S., & Faworaja, O. (2024). AI-driven demand forecasting: Enhancing inventory management and customer satisfaction. World Journal of Advanced Research and Reviews, 23(2), 100-110. 
Arnaiz, A. A. P., Cristal, L. S., Fernandez, A. O., Gubaton, M. R. F., Tanael, D. V., & Centeno, C. J. (2023). Optimizing inventory management and demand forecasting system using time series algorithm. World Journal of Advanced Research and Reviews, 20(3), 021-027. 
Avraam, D., Jones, E., & Burton, P. (2022). A deterministic approach for protecting privacy in sensitive personal data. BMC Medical Informatics and Decision Making, 22(1), 24. 
Ayer, T., White III, C. C., & Zhang, C. (2023). Healthcare inventory management. In Research Handbook on Inventory ManagemenT (pp. 431-454). Edward Elgar 
Publishing. 
Bhat, S. S., Srihari, V. R., Prabhune, A., Satheesh, S. S., & Bidrohi, A. B. (2024, January). 
Optimizing Medication Access in Public Healthcare Centers: A Machine Learning Stochastic Model for Inventory Management and Demand Forecasting in Primary 
Health Services. In 2024 International Conference on Intelligent and Innovative Technologies in Computing, Electrical and Electronics (IITCEE) (pp. 1-5). IEEE. 
Bhattacharjee, S., Rahim, L. B. A., Watada, J., & Roy, A. (2020). Unified GPU technique to boost confidentiality, integrity and trim data loss in big data transmission. IEEE Access, 8, 45477-45495. 
Brancato, V., Esposito, G., Coppola, L., Cavaliere, C., Mirabelli, P., Scapicchio, C., 
Borgheresi, R., Neri, E., & Salvatore, M. (2024). Standardizing digital biobanks: Integrating imaging, genomic, and clinical data for precision medicine. Journal of Translational Medicine, 22(1), 31. https://doi.org/10.1186/s12967-024-04891-8 
Burinskiene, A. (2022). Forecasting model: The case of the pharmaceutical retail. Frontiers in Medicine, 9, 582186. 
Costa, L., Hong, T. Y., Campbell, J., & Lin, A. (2024). MSR74 The Effect of Applying a 
Demand Forecasting Model to Assess the Accuracy of Inventory Management in a Specialty Pharmacy. Value in Health, 27(6), S273. 
Deekshitha, G., Bhat, G. A., Mahesh, G., Reddy, D. R., & Ayesha, I. Pharmaceutical 
	Inventory 	Management: 	Categorization 	Methods 	and 	Seasonal 	Demand 
Forecasting. 
Eldred, M. E., Thatcher, J., Rehman, A., Gee, I., & Suboyin, A. (2023, January). 
Leveraging AI for inventory management and accurate forecast–an industrial field 
study. In SPE Middle East Intelligent Oil and Gas Symposium (p. D011S001R001). SPE. 
Feretzakis, G., Sakagianni, A., Anastasiou, A., Kapogianni, I., Tsoni, R., Koufopoulou, C., & Verykios, V. S. (2024). Machine learning in medical triage: A predictive model for emergency department disposition. Applied Sciences, 14(15), 6623. https://doi.org/10.3390/app14156623 
Ganie, S. M., Pramanik, P. K. D., Malik, B., Mallik, M. S., & Qin, H. (2023). An ensemble learning approach for diabetes prediction using boosting techniques. Frontiers in Genetics, 14, 1252159. https://doi.org/10.3389/fgene.2023.1252159 
George, S., & Elrashid, S. (2023). Inventory management and pharmaceutical supply chain performance of hospital pharmacies in Bahrain: a structural equation modeling  
Hao, R. (2024, October). Streamlining SCM: Integrating Demand Forecasting and 
Inventory Optimization. In 2024 2nd International Conference on Management 
Innovation and Economy Development (MIED 2024) (pp. 550-558). Atlantis Press. 
Huang, B., Gan, W., & Li, Z. (2021). Application of medical material inventory model under deep learning in supply planning of public emergency. IEEE Access, 9, 44128-44138. 
Iannantuono, G. M., Bracken-Clarke, D., Floudas, C. S., Roselli, M., Gulley, J. L., & Karzai, F. (2023). Applications of large language models in cancer care: Current evidence and future perspectives. Frontiers in Oncology, 13, 1268915. 
https://doi.org/10.3389/fonc.2023.1268915 
Keskin, B. (2022). Digital supply chain transformation at Jabil: integrated supply and demand planning via concurrent planning. The Case For Women, 1-15. 
Korkmaz, A., Bulut, S., Talan, T., Kosunalp, S., & Iliev, T. (2024). Enhancing firewall packet classification through artificial neural networks and synthetic minority oversampling technique: An innovative approach with evaluative comparison. Applied Sciences, 14(16), 7426. https://doi.org/10.3390/app14167426 
Kraljevic, R. A., & Juanatas, I. C. (2024, March). Inventory Optimization through Sales Forecasting. In Proceedings of the 2024 15th International Conference on EEducation, E-Business, E-Management and E-Learning (pp. 330-336). 
Kraljevic, Z., Yeung, J. A., Bean, D., Teo, J., & Dobson, R. J. (2024). Large Language 
Models for Medical Forecasting--Foresight 2. arXiv preprint arXiv:2412.10848. 
Kumar, L., Khedlekar, S., & Khedlekar, U. K. (2024). A comparative assessment of holt winter exponential smoothing and autoregressive integrated moving average for inventory optimization in supply chains. Supply Chain Analytics, 8, 100084. https://doi.org/10.1016/j.sca.2024.100084 
Mirescu, L., & Popescu, L. (2024). Forecasts of Performance Indicators in the Health System 
Using the Arima Method. Journal of Social and Economic Statistics, 13(1). 
Mousa, B. A., & Al-Khateeb, B. (2023). Predicting medicine demand using deep learning techniques: 	A 	review. 	Journal 	of 	Intelligent 	Systems, 	32(1), 	31–40. 
https://doi.org/10.1515/jisys-2022-0297 
Muhamediyeva, D., Samijonov, A., Alimbaev, K., & Bakhtiyorov, S. (2024, May). Forecasting the market needs for medicines based on artificial intelligence technologies. In AIP Conference Proceedings (Vol. 3147, No. 1). AIP Publishing. 
Musimbi, P. M. (2022). A Predictive analytics model for pharmaceutical inventory management (Doctoral dissertation, Strathmore University). 
Octiva, C. S., Nuryanto, U. W., Eldo, H., & Tahir, A. (2024). Application of holt-winter exponential smoothing method to design a drug inventory prediction application in private health units. Jurnal Informasi Dan Teknologi, 1-6. 
Orhan, F., & Kurutkan, M. N. (2025). Predicting total healthcare demand using machine learning: Separate and combined analysis of predisposing, enabling, and need factors. BMC Health Services Research, 25, 366. https://doi.org/10.1186/s12913-
025-12502-5 
Pantha, R. P. (2023). Demand Prediction and Inventory Management of Surgical Supplies 
(Master's thesis, University of Arkansas). 
Poornima, G., Vinay, J., Karthikeyan, P., & Jinesh, V. N. (2024). Inventory tracking via IoT in the pharmaceutical industry. In Intelligent Wireless Sensor Networks and the Internet of Things (pp. 147-204). CRC Press. 
Rahiminia, M., Shahrabifarahani, S., Alipour-Vaezi, M., Aghsami, A., & Jolai, F. (2025). A novel data-driven patient and medical waste queueing-inventory system under pandemic: A real-life case study. International Journal of Production Research, 
63(2), 418-434. 
Razzak, M. A. (2020). GAME: Global agile model for enterprises (Doctoral dissertation, 
University of Limerick). 
Refonaa, J., Shabu, S. J., Paul, K. D., Dhamodaran, S., & Mary, V. A. (2023, July). 
MediStock: Medical Inventory Management System. In 2023 4th International 
Conference on Electronics and Sustainable Communication Systems (ICESC) (pp. 738-743). IEEE. 
Russo, D. (2021). The agile success model: A mixed-methods study of a large-scale agile transformation. ACM Transactions on Software Engineering and Methodology, 30(4), 1-38. https://doi.org/10.1145/3464938 
Saha, E., & Rathore, P. (2024). A smart inventory management system with medication demand dependencies in a hospital supply chain: A multi-agent reinforcement learning approach. Computers & Industrial Engineering, 191, 110165. 
Sakhare, K. V., & Kulkarni, I. (2022, May). Predictive analysis of end to end inventory management system for perishable goods. In 2022 3rd International Conference for Emerging Technology (INCET) (pp. 1-5). IEEE. 
Seyedan, M., Mafakheri, F., & Wang, C. (2023). Order-up-to-level inventory optimization model using time-series demand forecasting with ensemble deep learning. Supply Chain Analytics, 3, 100024. 
Sibindi, R., Mwangi, R. W., & Waititu, A. G. (2023). A boosting ensemble learning based hybrid light gradient boosting machine and extreme gradient boosting model for predicting house prices. Engineering Reports, 5(4), e12599. 
Sievering, A. W., Wohlmuth, P., Geßler, N., Gunawardene, M. A., Herrlinger, K., Bein, B., & Stang, A. (2022). Comparison of machine learning methods with logistic regression analysis in creating predictive models for risk of critical in-hospital events in COVID-19 patients on hospital admission. BMC Medical Informatics and Decision Making, 22(1), 309. https://doi.org/10.1186/s12911-022-02055-6 
Sowmiya, R. (2024). Predicting shelf-life of fruits: A multifactorial approach using intrinsic and extrinsic determinants. International Journal for Research in Applied Science 
	and 	Engineering 	Technology, 	12(3), 	81–90. 
https://doi.org/10.22214/ijraset.2024.59163 
Srilakshmi, U., Manikandan, J., Velagapudi, T., Abhinav, G., Kumar, T., & Saideep, D. (2024). A new approach to computationally-successful linear and polynomial regression analytics of large data in medicine. Journal of Computer Allied Intelligence (JCAI, ISSN: 2584-2676), 2(2), 35-48. 
Su, Z., Chow, J. K., Tan, P. S., Wu, J., Ho, Y. K., & Wang, Y.-H. (2020). Deep convolutional neural network–based pixel-wise landslide inventory mapping. Landslides, 17(4), 935–947. https://doi.org/10.1007/s10346-020-01557-6 
Subramanian, L. (2021). Effective demand forecasting in health supply chains: emerging trend, enablers, and blockers. Logistics, 5(1), 12. 
Sumathi, M., Dhiraj, I., Mahita, D. S., & Raja, S. P. (2023). Appointment Booking and Drug 
Inventory System in Healthcare Services Using Blockchain Technology. ADCAIJ: Advances in Distributed Computing and Artificial Intelligence Journal, 12, e31607e31607. 
Ternero, R., Sepúlveda-Rojas, J. P., Alfaro, M., Fuertes, G., & Vargas, M. (2023). Inventory management with stochastic demand: case study of a medical equipment company. South African Journal of Industrial Engineering, 34(1), 131-142. 
Tesfaye, S. H., Seboka, B. T., & Sisay, D. (2024). Application of machine learning methods for predicting childhood anaemia: Analysis of Ethiopian demographic health survey 
	of 	2016. 	PLOS 	ONE, 	19(4), 	e0300172. 
https://doi.org/10.1371/journal.pone.0300172 
Verma, P. (2024). Transforming Supply Chains Through AI: Demand Forecasting, Inventory Management, and Dynamic Optimization. Integrated Journal of Science and Technology, 1(9). 
Yawara, P., Supattananon, N., Siwapornrak, P., & Akararungruangkul, R. (2023). Purchasing planning for pharmaceuticals inventory: a case study of drug warehouse in hospital. Indones. J. Electr. Eng. Comput. Sci, 31(3), 1496-1506. 
 
 
 	 
Appendix A 
REC Protocol Approval 
 
 
 
 
 
 
 
 
Appendix B
Consent Form for the Research Participants 
 
 
Appendix C
Informed Consent 
 
 
 
 
 
Appendix D
Instruments of the Study 
 


 
Curriculum Vitae 
 
 
 
 
 
